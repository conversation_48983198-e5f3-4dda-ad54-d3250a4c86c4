"""
AI客户端模块
"""
import json
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

import openai
from anthropic import Anthropic
from loguru import logger

from config import settings


class BaseAIClient(ABC):
    """AI客户端基类"""
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """生成AI响应"""
        pass
    
    @abstractmethod
    async def extract_json(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """提取JSON格式响应"""
        pass


class OpenAIClient(BaseAIClient):
    """OpenAI客户端"""
    
    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.model = model or settings.DEFAULT_AI_MODEL
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        openai.api_key = self.api_key
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成AI响应
        
        Args:
            prompt: 输入提示
            **kwargs: 其他参数
            
        Returns:
            AI响应文本
        """
        try:
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=kwargs.get("temperature", 0.7),
                max_tokens=kwargs.get("max_tokens", 2000)
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise
    
    async def extract_json(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        提取JSON格式响应
        
        Args:
            prompt: 输入提示
            **kwargs: 其他参数
            
        Returns:
            解析后的JSON数据
        """
        response_text = await self.generate_response(prompt, **kwargs)
        
        try:
            # 尝试直接解析JSON
            return json.loads(response_text)
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass
            
            logger.warning(f"无法解析JSON响应: {response_text}")
            return {"error": "无法解析JSON响应", "raw_response": response_text}


class AnthropicClient(BaseAIClient):
    """Anthropic Claude客户端"""
    
    def __init__(self, api_key: str = None, model: str = "claude-3-sonnet-20240229"):
        self.api_key = api_key or settings.ANTHROPIC_API_KEY
        self.model = model
        
        if not self.api_key:
            raise ValueError("Anthropic API key is required")
        
        self.client = Anthropic(api_key=self.api_key)
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成AI响应
        
        Args:
            prompt: 输入提示
            **kwargs: 其他参数
            
        Returns:
            AI响应文本
        """
        try:
            response = await self.client.messages.create(
                model=self.model,
                max_tokens=kwargs.get("max_tokens", 2000),
                temperature=kwargs.get("temperature", 0.7),
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {e}")
            raise
    
    async def extract_json(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        提取JSON格式响应
        
        Args:
            prompt: 输入提示
            **kwargs: 其他参数
            
        Returns:
            解析后的JSON数据
        """
        response_text = await self.generate_response(prompt, **kwargs)
        
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass
            
            logger.warning(f"无法解析JSON响应: {response_text}")
            return {"error": "无法解析JSON响应", "raw_response": response_text}


class AIClientFactory:
    """AI客户端工厂"""
    
    @staticmethod
    def create_client(client_type: str = "openai", **kwargs) -> BaseAIClient:
        """
        创建AI客户端
        
        Args:
            client_type: 客户端类型 ("openai" 或 "anthropic")
            **kwargs: 其他参数
            
        Returns:
            AI客户端实例
        """
        if client_type.lower() == "openai":
            return OpenAIClient(**kwargs)
        elif client_type.lower() == "anthropic":
            return AnthropicClient(**kwargs)
        else:
            raise ValueError(f"不支持的AI客户端类型: {client_type}")
    
    @staticmethod
    def get_default_client() -> BaseAIClient:
        """
        获取默认AI客户端
        
        Returns:
            默认AI客户端实例
        """
        # 优先使用OpenAI，如果没有API key则使用Anthropic
        if settings.OPENAI_API_KEY:
            return OpenAIClient()
        elif settings.ANTHROPIC_API_KEY:
            return AnthropicClient()
        else:
            raise ValueError("没有配置可用的AI API密钥")
