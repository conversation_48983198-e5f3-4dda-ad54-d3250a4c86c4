#!/usr/bin/env python3
"""
MySQL数据库初始化脚本
"""
import pymysql
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config import settings


def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {settings.MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 '{settings.MYSQL_DATABASE}' 创建成功或已存在")
            
            # 创建用户（如果需要）
            # cursor.execute(f"CREATE USER IF NOT EXISTS '{settings.MYSQL_USER}'@'%' IDENTIFIED BY '{settings.MYSQL_PASSWORD}'")
            # cursor.execute(f"GRANT ALL PRIVILEGES ON {settings.MYSQL_DATABASE}.* TO '{settings.MYSQL_USER}'@'%'")
            # cursor.execute("FLUSH PRIVILEGES")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"创建数据库失败: {e}")
        return False


def create_tables():
    """创建数据表"""
    try:
        from database.models import init_database
        init_database()
        print("数据表创建成功")
        return True
    except Exception as e:
        print(f"创建数据表失败: {e}")
        return False


def main():
    """主函数"""
    print("开始初始化MySQL数据库...")
    
    # 检查配置
    if not all([settings.MYSQL_HOST, settings.MYSQL_USER, settings.MYSQL_DATABASE]):
        print("错误: 请在.env文件中配置MySQL连接信息")
        return False
    
    print(f"连接信息:")
    print(f"  主机: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}")
    print(f"  用户: {settings.MYSQL_USER}")
    print(f"  数据库: {settings.MYSQL_DATABASE}")
    
    # 创建数据库
    if not create_database():
        return False
    
    # 创建数据表
    if not create_tables():
        return False
    
    print("MySQL数据库初始化完成！")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
