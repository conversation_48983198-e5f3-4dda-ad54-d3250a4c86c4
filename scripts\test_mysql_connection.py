#!/usr/bin/env python3
"""
测试MySQL连接的脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

import pymysql
from config import settings


def test_mysql_connection():
    """测试MySQL连接"""
    try:
        print("测试MySQL连接...")
        print(f"主机: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}")
        print(f"用户: {settings.MYSQL_USER}")
        print(f"数据库: {settings.MYSQL_DATABASE}")
        
        # 测试连接到MySQL服务器
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"MySQL版本: {version[0]}")
            
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{settings.MYSQL_DATABASE}'")
            db_exists = cursor.fetchone()
            
            if db_exists:
                print(f"数据库 '{settings.MYSQL_DATABASE}' 存在")
                
                # 连接到指定数据库
                connection.select_db(settings.MYSQL_DATABASE)
                
                # 检查表是否存在
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print("现有数据表:")
                    for table in tables:
                        print(f"  - {table[0]}")
                else:
                    print("数据库中没有表")
            else:
                print(f"数据库 '{settings.MYSQL_DATABASE}' 不存在")
        
        connection.close()
        print("MySQL连接测试成功！")
        return True
        
    except Exception as e:
        print(f"MySQL连接测试失败: {e}")
        return False


def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    try:
        print("\n测试SQLAlchemy连接...")
        
        from database.models import engine, SessionLocal
        
        # 测试引擎连接
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print("SQLAlchemy引擎连接成功")
        
        # 测试会话
        session = SessionLocal()
        try:
            result = session.execute("SELECT 1")
            print("SQLAlchemy会话连接成功")
        finally:
            session.close()
        
        return True
        
    except Exception as e:
        print(f"SQLAlchemy连接测试失败: {e}")
        return False


def main():
    """主函数"""
    print("开始MySQL连接测试...\n")
    
    # 检查配置
    if not all([settings.MYSQL_HOST, settings.MYSQL_USER, settings.MYSQL_DATABASE]):
        print("错误: 请在.env文件中配置MySQL连接信息")
        return False
    
    # 测试基本连接
    if not test_mysql_connection():
        return False
    
    # 测试SQLAlchemy连接
    if not test_sqlalchemy_connection():
        return False
    
    print("\n所有连接测试通过！")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
