#!/usr/bin/env python3
"""
检查MySQL配置的脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))


def check_env_file():
    """检查.env文件配置"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env文件不存在")
        print("请复制.env.example到.env并配置MySQL连接信息")
        return False
    
    print("✅ .env文件存在")
    
    # 检查必要的MySQL配置
    required_vars = [
        "MYSQL_HOST",
        "MYSQL_PORT", 
        "MYSQL_USER",
        "MYSQL_PASSWORD",
        "MYSQL_DATABASE"
    ]
    
    missing_vars = []
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少以下MySQL配置变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ MySQL配置变量完整")
    return True


def check_dependencies():
    """检查Python依赖"""
    try:
        import pymysql
        print("✅ pymysql已安装")
    except ImportError:
        print("❌ pymysql未安装，请运行: pip install pymysql")
        return False
    
    try:
        import MySQLdb
        print("✅ mysqlclient已安装")
    except ImportError:
        print("⚠️  mysqlclient未安装，建议安装: pip install mysqlclient")
        print("   (可选，但推荐用于生产环境)")
    
    return True


def check_mysql_service():
    """检查MySQL服务状态"""
    try:
        from config import settings
        import pymysql
        
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            charset='utf8mb4',
            connect_timeout=5
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ MySQL服务运行正常，版本: {version[0]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        print("请检查:")
        print("  1. MySQL服务是否启动")
        print("  2. 用户名和密码是否正确")
        print("  3. 主机和端口是否正确")
        print("  4. 用户是否有连接权限")
        return False


def check_database():
    """检查数据库是否存在"""
    try:
        from config import settings
        import pymysql
        
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW DATABASES LIKE '{settings.MYSQL_DATABASE}'")
            db_exists = cursor.fetchone()
            
            if db_exists:
                print(f"✅ 数据库 '{settings.MYSQL_DATABASE}' 存在")
                
                # 检查字符集
                cursor.execute(f"""
                    SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
                    FROM information_schema.SCHEMATA 
                    WHERE SCHEMA_NAME = '{settings.MYSQL_DATABASE}'
                """)
                charset_info = cursor.fetchone()
                
                if charset_info:
                    charset, collation = charset_info
                    if charset == 'utf8mb4':
                        print(f"✅ 数据库字符集正确: {charset}")
                    else:
                        print(f"⚠️  数据库字符集: {charset} (建议使用utf8mb4)")
                
                return True
            else:
                print(f"❌ 数据库 '{settings.MYSQL_DATABASE}' 不存在")
                print(f"请运行: python run.py init-mysql")
                return False
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False


def main():
    """主函数"""
    print("MySQL配置检查工具")
    print("=" * 50)
    
    all_ok = True
    
    # 检查.env文件
    print("\n1. 检查环境配置文件...")
    if not check_env_file():
        all_ok = False
    
    # 检查Python依赖
    print("\n2. 检查Python依赖...")
    if not check_dependencies():
        all_ok = False
    
    # 检查MySQL服务
    print("\n3. 检查MySQL服务...")
    if not check_mysql_service():
        all_ok = False
        return False
    
    # 检查数据库
    print("\n4. 检查数据库...")
    if not check_database():
        all_ok = False
    
    print("\n" + "=" * 50)
    if all_ok:
        print("✅ 所有检查通过！MySQL配置正确。")
        print("您可以开始使用系统了:")
        print("  python run.py expert    # 启动专家界面")
        print("  python run.py server    # 启动Web服务")
    else:
        print("❌ 发现配置问题，请根据上述提示进行修复。")
    
    return all_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
