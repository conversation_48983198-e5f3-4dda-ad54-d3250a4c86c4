"""
AI提示词生成模块
"""
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from loguru import logger


@dataclass
class ExpertInput:
    """专家输入的结构化数据"""
    industry: str
    material_type: str
    key_points: List[str]
    focus_areas: List[str]
    extraction_goals: List[str]
    special_requirements: Optional[str] = None


class PromptGenerator:
    """AI提示词生成器"""
    
    def __init__(self):
        self.base_templates = {
            "knowledge_extraction": """
你是一个专业的{industry}行业知识提取专家。请仔细阅读以下{material_type}内容，并按照以下要求提取关键信息：

## 提取重点：
{key_points}

## 关注领域：
{focus_areas}

## 提取目标：
{extraction_goals}

{special_requirements}

## 输出格式要求：
请以JSON格式输出提取结果，包含以下字段：
- title: 文档标题或主题
- keywords: 关键词列表
- summary: 内容摘要
- key_insights: 核心洞察
- industry_specific_info: 行业特定信息
- actionable_items: 可执行的要点

## 待分析内容：
{content}

请开始分析：
""",
            
            "title_extraction": """
请为以下{industry}行业的{material_type}内容生成一个准确、简洁的标题。

## 内容特点：
{key_points}

## 标题要求：
1. 准确反映内容主题
2. 包含行业关键词
3. 长度控制在20字以内
4. 具有专业性和可读性

## 内容：
{content}

请生成标题：
""",
            
            "keyword_extraction": """
作为{industry}行业专家，请从以下{material_type}内容中提取关键词。

## 关键词类型：
{key_points}

## 提取要求：
1. 提取5-15个关键词
2. 包含行业术语和专业概念
3. 按重要性排序
4. 避免过于通用的词汇

## 内容：
{content}

请以JSON数组格式输出关键词：
"""
        }
    
    def generate_extraction_prompt(self, expert_input: ExpertInput, content: str) -> str:
        """
        生成知识提取的prompt
        
        Args:
            expert_input: 专家输入
            content: 待分析内容
            
        Returns:
            生成的prompt
        """
        template = self.base_templates["knowledge_extraction"]
        
        # 格式化关键点
        key_points_text = self._format_list_items(expert_input.key_points, "重点")
        focus_areas_text = self._format_list_items(expert_input.focus_areas, "领域")
        extraction_goals_text = self._format_list_items(expert_input.extraction_goals, "目标")
        
        # 处理特殊要求
        special_requirements_text = ""
        if expert_input.special_requirements:
            special_requirements_text = f"\n## 特殊要求：\n{expert_input.special_requirements}\n"
        
        return template.format(
            industry=expert_input.industry,
            material_type=expert_input.material_type,
            key_points=key_points_text,
            focus_areas=focus_areas_text,
            extraction_goals=extraction_goals_text,
            special_requirements=special_requirements_text,
            content=content[:4000]  # 限制内容长度
        )
    
    def generate_title_prompt(self, expert_input: ExpertInput, content: str) -> str:
        """
        生成标题提取的prompt
        
        Args:
            expert_input: 专家输入
            content: 待分析内容
            
        Returns:
            生成的prompt
        """
        template = self.base_templates["title_extraction"]
        
        key_points_text = self._format_list_items(expert_input.key_points, "特点")
        
        return template.format(
            industry=expert_input.industry,
            material_type=expert_input.material_type,
            key_points=key_points_text,
            content=content[:2000]  # 标题提取用较短内容
        )
    
    def generate_keyword_prompt(self, expert_input: ExpertInput, content: str) -> str:
        """
        生成关键词提取的prompt
        
        Args:
            expert_input: 专家输入
            content: 待分析内容
            
        Returns:
            生成的prompt
        """
        template = self.base_templates["keyword_extraction"]
        
        key_points_text = self._format_list_items(expert_input.key_points, "类型")
        
        return template.format(
            industry=expert_input.industry,
            material_type=expert_input.material_type,
            key_points=key_points_text,
            content=content[:3000]  # 关键词提取用中等长度内容
        )
    
    def _format_list_items(self, items: List[str], prefix: str = "项目") -> str:
        """
        格式化列表项目
        
        Args:
            items: 项目列表
            prefix: 前缀描述
            
        Returns:
            格式化后的文本
        """
        if not items:
            return f"无特定{prefix}要求"
        
        formatted_items = []
        for i, item in enumerate(items, 1):
            formatted_items.append(f"{i}. {item}")
        
        return "\n".join(formatted_items)
    
    def create_custom_prompt(
        self,
        industry: str,
        material_type: str,
        template: str,
        variables: Dict[str, Any]
    ) -> str:
        """
        创建自定义prompt
        
        Args:
            industry: 行业
            material_type: 材料类型
            template: 模板字符串
            variables: 变量字典
            
        Returns:
            生成的prompt
        """
        try:
            # 添加基础变量
            variables.update({
                "industry": industry,
                "material_type": material_type
            })
            
            return template.format(**variables)
            
        except KeyError as e:
            logger.error(f"模板变量缺失: {e}")
            raise ValueError(f"模板变量缺失: {e}")
    
    def save_prompt_template(
        self,
        name: str,
        template: str,
        description: str = ""
    ) -> bool:
        """
        保存prompt模板
        
        Args:
            name: 模板名称
            template: 模板内容
            description: 模板描述
            
        Returns:
            是否保存成功
        """
        try:
            self.base_templates[name] = template
            logger.info(f"保存prompt模板: {name}")
            return True
        except Exception as e:
            logger.error(f"保存prompt模板失败: {e}")
            return False
    
    def get_available_templates(self) -> List[str]:
        """
        获取可用的模板列表
        
        Returns:
            模板名称列表
        """
        return list(self.base_templates.keys())
