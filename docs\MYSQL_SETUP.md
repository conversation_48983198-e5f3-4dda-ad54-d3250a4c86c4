# MySQL数据库配置指南

本文档详细说明如何配置MySQL数据库以支持行业知识库AI构建工具。

## 1. MySQL安装

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install mysql-server
sudo mysql_secure_installation
```

### CentOS/RHEL
```bash
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
sudo mysql_secure_installation
```

### macOS (使用Homebrew)
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

### Windows
1. 下载MySQL安装包: https://dev.mysql.com/downloads/mysql/
2. 运行安装程序
3. 配置root密码
4. 启动MySQL服务

## 2. 数据库和用户配置

### 登录MySQL
```bash
mysql -u root -p
```

### 创建数据库
```sql
-- 创建数据库，使用utf8mb4字符集支持中文
CREATE DATABASE knowledge_base CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 创建专用用户（推荐）
```sql
-- 创建用户
CREATE USER 'kb_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON knowledge_base.* TO 'kb_user'@'localhost';

-- 如果需要远程访问
CREATE USER 'kb_user'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON knowledge_base.* TO 'kb_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 验证配置
```sql
-- 查看数据库
SHOW DATABASES;

-- 查看用户
SELECT User, Host FROM mysql.user WHERE User = 'kb_user';

-- 查看权限
SHOW GRANTS FOR 'kb_user'@'localhost';
```

## 3. 环境变量配置

创建或编辑 `.env` 文件：

```env
# MySQL数据库配置
DATABASE_URL=mysql+pymysql://kb_user:your_secure_password@localhost:3306/knowledge_base
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=kb_user
MYSQL_PASSWORD=your_secure_password
MYSQL_DATABASE=knowledge_base

# AI API配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_AI_MODEL=gpt-3.5-turbo

# 其他配置...
```

## 4. Python依赖安装

```bash
# 安装MySQL相关依赖
pip install pymysql mysqlclient

# 或者安装所有依赖
pip install -r requirements.txt
```

**注意**: 在某些系统上安装 `mysqlclient` 可能需要额外的系统依赖：

### Ubuntu/Debian
```bash
sudo apt-get install python3-dev default-libmysqlclient-dev build-essential
```

### CentOS/RHEL
```bash
sudo yum install python3-devel mysql-devel gcc
```

### macOS
```bash
brew install mysql-client
export PATH="/usr/local/opt/mysql-client/bin:$PATH"
```

## 5. 初始化数据库

```bash
# 测试MySQL连接
python run.py test-mysql

# 初始化MySQL数据库
python run.py init-mysql

# 或者使用主程序
python main.py init --mysql
```

## 6. 数据迁移

如果您之前使用SQLite，可以迁移数据：

```bash
# 从SQLite迁移到MySQL
python main.py migrate knowledge_base.db
```

## 7. 性能优化配置

### MySQL配置优化 (my.cnf)

```ini
[mysqld]
# 字符集配置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 连接配置
max_connections=200
max_connect_errors=10

# 缓冲区配置
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
innodb_log_buffer_size=16M

# 查询缓存
query_cache_type=1
query_cache_size=64M

# 临时表配置
tmp_table_size=64M
max_heap_table_size=64M
```

### 应用层配置

在 `config.py` 中调整连接池设置：

```python
# 数据库连接池配置
engine = create_engine(
    settings.get_database_url(),
    echo=settings.DEBUG,
    poolclass=QueuePool,
    pool_size=20,          # 连接池大小
    max_overflow=30,       # 最大溢出连接
    pool_pre_ping=True,    # 连接前ping
    pool_recycle=3600,     # 连接回收时间
    connect_args={
        "charset": "utf8mb4",
        "autocommit": False
    }
)
```

## 8. 监控和维护

### 查看连接状态
```sql
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
```

### 查看数据库大小
```sql
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'knowledge_base'
GROUP BY table_schema;
```

### 备份数据库
```bash
# 备份
mysqldump -u kb_user -p knowledge_base > backup.sql

# 恢复
mysql -u kb_user -p knowledge_base < backup.sql
```

## 9. 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查MySQL服务是否启动
   - 检查防火墙设置
   - 验证用户名和密码

2. **字符编码问题**
   - 确保数据库使用utf8mb4字符集
   - 检查连接字符串中的charset参数

3. **权限问题**
   - 检查用户权限设置
   - 确认用户可以从指定主机连接

4. **性能问题**
   - 检查连接池配置
   - 监控数据库查询性能
   - 考虑添加索引

### 调试命令

```bash
# 测试连接
python scripts/test_mysql_connection.py

# 查看配置
python -c "from config import settings; print(settings.get_database_url())"

# 检查表结构
python -c "from database.models import engine; print(engine.table_names())"
```

## 10. 安全建议

1. **使用强密码**: 为MySQL用户设置复杂密码
2. **限制访问**: 只允许必要的主机连接
3. **定期备份**: 建立自动备份机制
4. **监控日志**: 定期检查MySQL错误日志
5. **更新维护**: 保持MySQL版本更新

## 11. 生产环境部署

### 高可用配置
- 考虑使用MySQL主从复制
- 配置读写分离
- 使用负载均衡器

### 监控配置
- 设置性能监控
- 配置告警机制
- 定期健康检查

通过以上配置，您的MySQL数据库就可以支持行业知识库AI构建工具了。如有问题，请参考故障排除部分或联系技术支持。
