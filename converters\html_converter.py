"""
HTML文档转换器
"""
import re
from pathlib import Path
from typing import Dict, Any

from bs4 import BeautifulSoup
import html2text
from loguru import logger

from .base import BaseConverter, ConversionError


class HTMLConverter(BaseConverter):
    """HTML转换器"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.html', '.htm'}
        
        # 配置html2text
        self.h = html2text.HTML2Text()
        self.h.ignore_links = False
        self.h.ignore_images = False
        self.h.body_width = 0  # 不限制行宽
        self.h.unicode_snob = True
    
    def convert_to_markdown(self, file_path: Path) -> str:
        """
        将HTML转换为Markdown
        
        Args:
            file_path: HTML文件路径
            
        Returns:
            Markdown内容
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()
            
            # 使用BeautifulSoup清理HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取主要内容（尝试找到main, article, content等标签）
            main_content = self._extract_main_content(soup)
            
            # 转换为Markdown
            markdown_content = self.h.handle(str(main_content))
            
            # 清理和格式化
            markdown_content = self._clean_markdown(markdown_content)
            
            return markdown_content
            
        except Exception as e:
            logger.error(f"HTML转换失败: {e}")
            raise ConversionError(f"HTML转换失败: {e}")
    
    def _extract_main_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """
        提取HTML的主要内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            主要内容的BeautifulSoup对象
        """
        # 尝试找到主要内容区域
        main_selectors = [
            'main',
            'article',
            '.content',
            '#content',
            '.main-content',
            '#main-content',
            '.post-content',
            '.entry-content',
            'body'
        ]
        
        for selector in main_selectors:
            main_element = soup.select_one(selector)
            if main_element:
                return main_element
        
        # 如果没找到特定的内容区域，返回整个body
        return soup.find('body') or soup
    
    def _clean_markdown(self, markdown: str) -> str:
        """
        清理Markdown内容
        
        Args:
            markdown: 原始Markdown
            
        Returns:
            清理后的Markdown
        """
        # 移除多余的空行
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
        
        # 清理多余的空格
        lines = []
        for line in markdown.split('\n'):
            lines.append(line.rstrip())
        
        return '\n'.join(lines).strip()
    
    def get_metadata(self, file_path: Path) -> Dict[str, Any]:
        """获取HTML元数据"""
        metadata = super().get_metadata(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取meta信息
            title = soup.find('title')
            description = soup.find('meta', attrs={'name': 'description'})
            keywords = soup.find('meta', attrs={'name': 'keywords'})
            author = soup.find('meta', attrs={'name': 'author'})
            
            metadata.update({
                "title": title.get_text().strip() if title else "",
                "description": description.get('content', '') if description else "",
                "keywords": keywords.get('content', '') if keywords else "",
                "author": author.get('content', '') if author else "",
                "has_images": len(soup.find_all('img')) > 0,
                "has_tables": len(soup.find_all('table')) > 0,
                "link_count": len(soup.find_all('a')),
                "image_count": len(soup.find_all('img'))
            })
            
        except Exception as e:
            logger.warning(f"获取HTML元数据失败: {e}")
        
        return metadata
