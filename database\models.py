"""
数据库模型定义
"""
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.pool import QueuePool

from config import settings

Base = declarative_base()


class KnowledgeBase(Base):
    """知识库信息表"""
    __tablename__ = "knowledge_bases"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    industry = Column(String(100), nullable=False, index=True, comment="行业")
    title = Column(String(500), nullable=False, comment="标题")
    keywords = Column(Text, comment="关键词，JSON格式存储")
    description = Column(Text, comment="描述")
    knowledge_source = Column(String(500), comment="知识来源")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联的知识要点
    knowledge_points = relationship("IndustryKnowledgePoint", back_populates="knowledge_base")


class IndustryKnowledgePoint(Base):
    """行业资料知识要点表"""
    __tablename__ = "industry_knowledge_points"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    knowledge_base_id = Column(Integer, ForeignKey("knowledge_bases.id"), nullable=True, comment="关联知识库ID")
    industry = Column(String(100), nullable=False, index=True, comment="行业")
    material_type = Column(String(50), nullable=False, comment="资料类型")
    knowledge_point = Column(Text, nullable=False, comment="知识要点")
    prompt = Column(Text, nullable=False, comment="提取prompt")
    is_manually_modified = Column(Boolean, default=False, comment="是否人工修改")
    is_active = Column(Boolean, default=True, comment="当前可用")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联的知识库
    knowledge_base = relationship("KnowledgeBase", back_populates="knowledge_points")


class ExpertTemplate(Base):
    """专家模板表 - 存储行业专家的经验模板"""
    __tablename__ = "expert_templates"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    industry = Column(String(100), nullable=False, index=True, comment="行业")
    material_type = Column(String(50), nullable=False, comment="资料类型")
    template_name = Column(String(200), nullable=False, comment="模板名称")
    key_points = Column(Text, nullable=False, comment="关键要点，JSON格式")
    prompt_template = Column(Text, nullable=False, comment="prompt模板")
    usage_count = Column(Integer, default=0, comment="使用次数")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_by = Column(String(100), comment="创建者")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")


# 数据库引擎和会话
engine = create_engine(
    settings.get_database_url(),
    echo=settings.DEBUG,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    connect_args={
        "charset": "utf8mb4",
        "autocommit": False
    }
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_database():
    """初始化数据库"""
    Base.metadata.create_all(bind=engine)
