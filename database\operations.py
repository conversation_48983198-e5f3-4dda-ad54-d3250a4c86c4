"""
数据库操作类
"""
import json
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from .models import KnowledgeBase, IndustryKnowledgePoint, ExpertTemplate


class KnowledgeBaseOperations:
    """知识库操作类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_knowledge_base(
        self,
        industry: str,
        title: str,
        keywords: List[str],
        description: str = None,
        knowledge_source: str = None
    ) -> KnowledgeBase:
        """创建知识库"""
        kb = KnowledgeBase(
            industry=industry,
            title=title,
            keywords=json.dumps(keywords, ensure_ascii=False),
            description=description,
            knowledge_source=knowledge_source
        )
        self.db.add(kb)
        self.db.commit()
        self.db.refresh(kb)
        return kb
    
    def get_knowledge_base(self, kb_id: int) -> Optional[KnowledgeBase]:
        """获取知识库"""
        return self.db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    
    def search_knowledge_bases(
        self,
        industry: str = None,
        keywords: List[str] = None,
        limit: int = 50
    ) -> List[KnowledgeBase]:
        """搜索知识库"""
        query = self.db.query(KnowledgeBase)
        
        if industry:
            query = query.filter(KnowledgeBase.industry == industry)
        
        if keywords:
            # 搜索关键词
            keyword_filters = []
            for keyword in keywords:
                keyword_filters.append(KnowledgeBase.keywords.contains(keyword))
                keyword_filters.append(KnowledgeBase.title.contains(keyword))
                keyword_filters.append(KnowledgeBase.description.contains(keyword))
            query = query.filter(or_(*keyword_filters))
        
        return query.limit(limit).all()


class KnowledgePointOperations:
    """知识要点操作类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_knowledge_point(
        self,
        industry: str,
        material_type: str,
        knowledge_point: str,
        prompt: str,
        knowledge_base_id: int = None,
        is_manually_modified: bool = False
    ) -> IndustryKnowledgePoint:
        """创建知识要点"""
        kp = IndustryKnowledgePoint(
            knowledge_base_id=knowledge_base_id,
            industry=industry,
            material_type=material_type,
            knowledge_point=knowledge_point,
            prompt=prompt,
            is_manually_modified=is_manually_modified
        )
        self.db.add(kp)
        self.db.commit()
        self.db.refresh(kp)
        return kp
    
    def update_knowledge_point(
        self,
        kp_id: int,
        knowledge_point: str = None,
        prompt: str = None,
        is_manually_modified: bool = True
    ) -> Optional[IndustryKnowledgePoint]:
        """更新知识要点"""
        kp = self.db.query(IndustryKnowledgePoint).filter(
            IndustryKnowledgePoint.id == kp_id
        ).first()
        
        if not kp:
            return None
        
        if knowledge_point is not None:
            kp.knowledge_point = knowledge_point
        if prompt is not None:
            kp.prompt = prompt
        
        kp.is_manually_modified = is_manually_modified
        kp.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(kp)
        return kp
    
    def get_knowledge_points_by_industry(
        self,
        industry: str,
        material_type: str = None,
        active_only: bool = True
    ) -> List[IndustryKnowledgePoint]:
        """根据行业获取知识要点"""
        query = self.db.query(IndustryKnowledgePoint).filter(
            IndustryKnowledgePoint.industry == industry
        )
        
        if material_type:
            query = query.filter(IndustryKnowledgePoint.material_type == material_type)
        
        if active_only:
            query = query.filter(IndustryKnowledgePoint.is_active == True)
        
        return query.all()


class ExpertTemplateOperations:
    """专家模板操作类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_template(
        self,
        industry: str,
        material_type: str,
        template_name: str,
        key_points: List[str],
        prompt_template: str,
        created_by: str = None
    ) -> ExpertTemplate:
        """创建专家模板"""
        template = ExpertTemplate(
            industry=industry,
            material_type=material_type,
            template_name=template_name,
            key_points=json.dumps(key_points, ensure_ascii=False),
            prompt_template=prompt_template,
            created_by=created_by
        )
        self.db.add(template)
        self.db.commit()
        self.db.refresh(template)
        return template
    
    def get_templates_by_industry(
        self,
        industry: str,
        material_type: str = None,
        active_only: bool = True
    ) -> List[ExpertTemplate]:
        """根据行业获取模板"""
        query = self.db.query(ExpertTemplate).filter(
            ExpertTemplate.industry == industry
        )
        
        if material_type:
            query = query.filter(ExpertTemplate.material_type == material_type)
        
        if active_only:
            query = query.filter(ExpertTemplate.is_active == True)
        
        return query.order_by(ExpertTemplate.usage_count.desc()).all()
    
    def increment_usage(self, template_id: int):
        """增加模板使用次数"""
        template = self.db.query(ExpertTemplate).filter(
            ExpertTemplate.id == template_id
        ).first()
        
        if template:
            template.usage_count += 1
            self.db.commit()
