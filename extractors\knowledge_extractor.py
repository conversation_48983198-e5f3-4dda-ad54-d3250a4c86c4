"""
知识点提取器
"""
import asyncio
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from loguru import logger

from core.prompt_generator import PromptGenerator, ExpertInput
from core.ai_client import AIClientFactory, BaseAIClient
from .text_splitter import TextSplitterFactory, TextChunk


@dataclass
class KnowledgePoint:
    """知识点"""
    title: str
    content: str
    keywords: List[str]
    summary: str
    industry_info: str
    source_chunk: TextChunk
    confidence: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ExtractionResult:
    """提取结果"""
    knowledge_points: List[KnowledgePoint]
    total_chunks: int
    successful_extractions: int
    failed_extractions: int
    processing_time: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class KnowledgeExtractor:
    """知识点提取器"""
    
    def __init__(
        self,
        ai_client: BaseAIClient = None,
        splitter_type: str = "semantic",
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ):
        self.ai_client = ai_client or AIClientFactory.get_default_client()
        self.prompt_generator = PromptGenerator()
        self.text_splitter = TextSplitterFactory.create_splitter(
            splitter_type, chunk_size, chunk_overlap
        )
    
    async def extract_knowledge_points(
        self,
        text: str,
        expert_input: ExpertInput,
        max_concurrent: int = 3
    ) -> ExtractionResult:
        """
        提取知识点
        
        Args:
            text: 输入文本
            expert_input: 专家输入
            max_concurrent: 最大并发数
            
        Returns:
            提取结果
        """
        import time
        start_time = time.time()
        
        # 分割文本
        chunks = self.text_splitter.split_text(text)
        logger.info(f"文本分割为 {len(chunks)} 个块")
        
        # 并发提取知识点
        semaphore = asyncio.Semaphore(max_concurrent)
        tasks = []
        
        for chunk in chunks:
            task = self._extract_from_chunk(chunk, expert_input, semaphore)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        knowledge_points = []
        successful_extractions = 0
        failed_extractions = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"块 {i} 提取失败: {result}")
                failed_extractions += 1
            elif result:
                knowledge_points.append(result)
                successful_extractions += 1
            else:
                failed_extractions += 1
        
        processing_time = time.time() - start_time
        
        return ExtractionResult(
            knowledge_points=knowledge_points,
            total_chunks=len(chunks),
            successful_extractions=successful_extractions,
            failed_extractions=failed_extractions,
            processing_time=processing_time,
            metadata={
                "expert_input": expert_input,
                "splitter_type": type(self.text_splitter).__name__,
                "chunk_size": self.text_splitter.chunk_size
            }
        )
    
    async def _extract_from_chunk(
        self,
        chunk: TextChunk,
        expert_input: ExpertInput,
        semaphore: asyncio.Semaphore
    ) -> Optional[KnowledgePoint]:
        """
        从单个文本块提取知识点
        
        Args:
            chunk: 文本块
            expert_input: 专家输入
            semaphore: 信号量
            
        Returns:
            知识点或None
        """
        async with semaphore:
            try:
                # 生成提取prompt
                prompt = self.prompt_generator.generate_extraction_prompt(
                    expert_input, chunk.content
                )
                
                # 调用AI提取
                response = await self.ai_client.extract_json(prompt)
                
                if "error" in response:
                    logger.warning(f"AI提取失败: {response['error']}")
                    return None
                
                # 解析响应
                knowledge_point = self._parse_extraction_response(response, chunk)
                return knowledge_point
                
            except Exception as e:
                logger.error(f"提取知识点失败: {e}")
                return None
    
    def _parse_extraction_response(
        self,
        response: Dict[str, Any],
        chunk: TextChunk
    ) -> KnowledgePoint:
        """
        解析AI提取响应
        
        Args:
            response: AI响应
            chunk: 源文本块
            
        Returns:
            知识点
        """
        return KnowledgePoint(
            title=response.get("title", "未知标题"),
            content=response.get("key_insights", chunk.content[:200] + "..."),
            keywords=response.get("keywords", []),
            summary=response.get("summary", ""),
            industry_info=response.get("industry_specific_info", ""),
            source_chunk=chunk,
            confidence=self._calculate_confidence(response),
            metadata={
                "raw_response": response,
                "actionable_items": response.get("actionable_items", [])
            }
        )
    
    def _calculate_confidence(self, response: Dict[str, Any]) -> float:
        """
        计算提取置信度
        
        Args:
            response: AI响应
            
        Returns:
            置信度分数 (0-1)
        """
        score = 0.0
        
        # 检查必要字段
        required_fields = ["title", "keywords", "summary"]
        for field in required_fields:
            if response.get(field):
                score += 0.2
        
        # 检查关键词数量
        keywords = response.get("keywords", [])
        if len(keywords) >= 3:
            score += 0.2
        elif len(keywords) >= 1:
            score += 0.1
        
        # 检查摘要长度
        summary = response.get("summary", "")
        if len(summary) >= 50:
            score += 0.2
        elif len(summary) >= 20:
            score += 0.1
        
        return min(score, 1.0)
    
    async def extract_title_and_keywords(
        self,
        text: str,
        expert_input: ExpertInput
    ) -> Dict[str, Any]:
        """
        提取标题和关键词
        
        Args:
            text: 输入文本
            expert_input: 专家输入
            
        Returns:
            包含标题和关键词的字典
        """
        try:
            # 使用文本的前2000字符
            content_preview = text[:2000]
            
            # 并发提取标题和关键词
            title_task = self._extract_title(content_preview, expert_input)
            keywords_task = self._extract_keywords(content_preview, expert_input)
            
            title, keywords = await asyncio.gather(title_task, keywords_task)
            
            return {
                "title": title,
                "keywords": keywords,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"提取标题和关键词失败: {e}")
            return {
                "title": "未知标题",
                "keywords": [],
                "success": False,
                "error": str(e)
            }
    
    async def _extract_title(self, content: str, expert_input: ExpertInput) -> str:
        """提取标题"""
        prompt = self.prompt_generator.generate_title_prompt(expert_input, content)
        response = await self.ai_client.generate_response(prompt)
        return response.strip()
    
    async def _extract_keywords(self, content: str, expert_input: ExpertInput) -> List[str]:
        """提取关键词"""
        prompt = self.prompt_generator.generate_keyword_prompt(expert_input, content)
        response = await self.ai_client.extract_json(prompt)
        
        if isinstance(response, list):
            return response
        elif isinstance(response, dict) and "keywords" in response:
            return response["keywords"]
        else:
            return []
