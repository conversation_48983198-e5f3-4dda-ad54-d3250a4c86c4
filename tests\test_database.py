"""
数据库操作测试
"""
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from database.models import Base, KnowledgeBase, IndustryKnowledgePoint, ExpertTemplate
from database.operations import KnowledgeBaseOperations, KnowledgePointOperations, ExpertTemplateOperations


@pytest.fixture
def test_db():
    """测试数据库"""
    # 使用内存SQLite数据库
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    yield session
    
    session.close()


class TestKnowledgeBaseOperations:
    """知识库操作测试"""
    
    def test_create_knowledge_base(self, test_db):
        """测试创建知识库"""
        kb_ops = KnowledgeBaseOperations(test_db)
        
        kb = kb_ops.create_knowledge_base(
            industry="科技",
            title="人工智能技术报告",
            keywords=["AI", "机器学习", "深度学习"],
            description="关于AI技术的详细报告",
            knowledge_source="test_report.pdf"
        )
        
        assert kb.id is not None
        assert kb.industry == "科技"
        assert kb.title == "人工智能技术报告"
        assert "AI" in kb.keywords
    
    def test_get_knowledge_base(self, test_db):
        """测试获取知识库"""
        kb_ops = KnowledgeBaseOperations(test_db)
        
        # 先创建一个知识库
        created_kb = kb_ops.create_knowledge_base(
            industry="金融",
            title="金融市场分析",
            keywords=["股票", "债券"],
            description="金融市场分析报告"
        )
        
        # 获取知识库
        retrieved_kb = kb_ops.get_knowledge_base(created_kb.id)
        
        assert retrieved_kb is not None
        assert retrieved_kb.id == created_kb.id
        assert retrieved_kb.industry == "金融"
    
    def test_search_knowledge_bases(self, test_db):
        """测试搜索知识库"""
        kb_ops = KnowledgeBaseOperations(test_db)
        
        # 创建多个知识库
        kb_ops.create_knowledge_base(
            industry="科技",
            title="AI技术报告",
            keywords=["AI", "技术"],
            description="人工智能技术分析"
        )
        
        kb_ops.create_knowledge_base(
            industry="金融",
            title="市场分析报告",
            keywords=["市场", "分析"],
            description="金融市场深度分析"
        )
        
        # 按行业搜索
        tech_kbs = kb_ops.search_knowledge_bases(industry="科技")
        assert len(tech_kbs) == 1
        assert tech_kbs[0].industry == "科技"
        
        # 按关键词搜索
        ai_kbs = kb_ops.search_knowledge_bases(keywords=["AI"])
        assert len(ai_kbs) >= 1
        assert any("AI" in kb.keywords for kb in ai_kbs)


class TestKnowledgePointOperations:
    """知识点操作测试"""
    
    def test_create_knowledge_point(self, test_db):
        """测试创建知识点"""
        kp_ops = KnowledgePointOperations(test_db)
        
        kp = kp_ops.create_knowledge_point(
            industry="医疗",
            material_type="临床报告",
            knowledge_point="患者诊断结果显示...",
            prompt="请提取诊断相关信息"
        )
        
        assert kp.id is not None
        assert kp.industry == "医疗"
        assert kp.material_type == "临床报告"
        assert "诊断" in kp.knowledge_point
    
    def test_update_knowledge_point(self, test_db):
        """测试更新知识点"""
        kp_ops = KnowledgePointOperations(test_db)
        
        # 创建知识点
        kp = kp_ops.create_knowledge_point(
            industry="教育",
            material_type="教学材料",
            knowledge_point="原始内容",
            prompt="原始prompt"
        )
        
        # 更新知识点
        updated_kp = kp_ops.update_knowledge_point(
            kp_id=kp.id,
            knowledge_point="更新后的内容",
            prompt="更新后的prompt"
        )
        
        assert updated_kp is not None
        assert updated_kp.knowledge_point == "更新后的内容"
        assert updated_kp.prompt == "更新后的prompt"
        assert updated_kp.is_manually_modified is True
    
    def test_get_knowledge_points_by_industry(self, test_db):
        """测试按行业获取知识点"""
        kp_ops = KnowledgePointOperations(test_db)
        
        # 创建不同行业的知识点
        kp_ops.create_knowledge_point(
            industry="科技",
            material_type="技术文档",
            knowledge_point="技术要点1",
            prompt="提取技术信息"
        )
        
        kp_ops.create_knowledge_point(
            industry="科技",
            material_type="研究报告",
            knowledge_point="技术要点2",
            prompt="提取研究信息"
        )
        
        kp_ops.create_knowledge_point(
            industry="金融",
            material_type="分析报告",
            knowledge_point="金融要点1",
            prompt="提取金融信息"
        )
        
        # 获取科技行业的知识点
        tech_kps = kp_ops.get_knowledge_points_by_industry("科技")
        assert len(tech_kps) == 2
        assert all(kp.industry == "科技" for kp in tech_kps)
        
        # 获取特定材料类型的知识点
        tech_docs = kp_ops.get_knowledge_points_by_industry("科技", "技术文档")
        assert len(tech_docs) == 1
        assert tech_docs[0].material_type == "技术文档"


class TestExpertTemplateOperations:
    """专家模板操作测试"""
    
    def test_create_template(self, test_db):
        """测试创建模板"""
        template_ops = ExpertTemplateOperations(test_db)
        
        template = template_ops.create_template(
            industry="制造业",
            material_type="生产报告",
            template_name="生产效率分析模板",
            key_points=["生产效率", "质量控制", "成本分析"],
            prompt_template="请分析生产相关数据...",
            created_by="专家A"
        )
        
        assert template.id is not None
        assert template.industry == "制造业"
        assert template.template_name == "生产效率分析模板"
        assert "生产效率" in template.key_points
    
    def test_get_templates_by_industry(self, test_db):
        """测试按行业获取模板"""
        template_ops = ExpertTemplateOperations(test_db)
        
        # 创建多个模板
        template_ops.create_template(
            industry="零售",
            material_type="销售报告",
            template_name="销售分析模板",
            key_points=["销售额", "客户分析"],
            prompt_template="分析销售数据..."
        )
        
        template_ops.create_template(
            industry="零售",
            material_type="库存报告",
            template_name="库存管理模板",
            key_points=["库存水平", "周转率"],
            prompt_template="分析库存数据..."
        )
        
        # 获取零售行业的模板
        retail_templates = template_ops.get_templates_by_industry("零售")
        assert len(retail_templates) == 2
        assert all(t.industry == "零售" for t in retail_templates)
        
        # 获取特定材料类型的模板
        sales_templates = template_ops.get_templates_by_industry("零售", "销售报告")
        assert len(sales_templates) == 1
        assert sales_templates[0].material_type == "销售报告"
    
    def test_increment_usage(self, test_db):
        """测试增加使用次数"""
        template_ops = ExpertTemplateOperations(test_db)
        
        # 创建模板
        template = template_ops.create_template(
            industry="物流",
            material_type="运输报告",
            template_name="运输效率模板",
            key_points=["运输时间", "成本效益"],
            prompt_template="分析运输数据..."
        )
        
        initial_count = template.usage_count
        
        # 增加使用次数
        template_ops.increment_usage(template.id)
        
        # 重新获取模板检查使用次数
        updated_template = test_db.query(ExpertTemplate).filter(
            ExpertTemplate.id == template.id
        ).first()
        
        assert updated_template.usage_count == initial_count + 1
