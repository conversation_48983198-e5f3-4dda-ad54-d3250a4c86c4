"""
行业知识库AI构建工具主入口
"""
import asyncio
from pathlib import Path

import click
from rich.console import Console
from rich.panel import Panel

from config import settings
from database.models import init_database
from cli.expert_interface import ExpertInterface
from api.server import create_app

console = Console()


@click.group()
def cli():
    """行业知识库AI构建工具"""
    pass


@cli.command()
def init():
    """初始化数据库"""
    console.print(Panel("正在初始化数据库...", style="blue"))
    init_database()
    console.print(Panel("数据库初始化完成！", style="green"))


@cli.command()
def expert():
    """启动专家交互界面"""
    console.print(Panel(f"欢迎使用{settings.APP_NAME}", style="blue"))
    interface = ExpertInterface()
    asyncio.run(interface.run())


@cli.command()
@click.option("--host", default="127.0.0.1", help="服务器主机地址")
@click.option("--port", default=8000, help="服务器端口")
def server(host: str, port: int):
    """启动Web API服务器"""
    import uvicorn
    
    console.print(Panel(f"启动Web服务器 http://{host}:{port}", style="blue"))
    app = create_app()
    uvicorn.run(app, host=host, port=port)


@cli.command()
@click.argument("file_path", type=click.Path(exists=True))
@click.option("--industry", required=True, help="行业类型")
@click.option("--expert-points", help="专家要点文件路径")
def process(file_path: str, industry: str, expert_points: str = None):
    """处理单个文件"""
    from core.processor import DocumentProcessor
    
    processor = DocumentProcessor()
    result = asyncio.run(processor.process_file(
        file_path=Path(file_path),
        industry=industry,
        expert_points_file=expert_points
    ))
    
    console.print(Panel(f"处理完成！提取了 {len(result)} 个知识点", style="green"))


if __name__ == "__main__":
    cli()
