"""
文档转换器工厂
"""
from pathlib import Path
from typing import Optional, List, Dict, Any

from loguru import logger

from .base import BaseConverter, ConversionError
from .pdf_converter import PDFConverter
from .word_converter import WordConverter
from .html_converter import HTMLConverter
from .image_converter import ImageConverter


class ConverterFactory:
    """文档转换器工厂类"""
    
    def __init__(self):
        self.converters = {
            'pdf': PDFConverter(),
            'word': WordConverter(),
            'html': HTMLConverter(),
            'image': ImageConverter()
        }
    
    def get_converter(self, file_path: Path) -> Optional[BaseConverter]:
        """
        根据文件类型获取对应的转换器
        
        Args:
            file_path: 文件路径
            
        Returns:
            对应的转换器，如果不支持则返回None
        """
        for converter in self.converters.values():
            if converter.can_convert(file_path):
                return converter
        
        return None
    
    def convert_file(self, file_path: Path) -> Dict[str, Any]:
        """
        转换文件为Markdown
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含转换结果和元数据的字典
        """
        if not file_path.exists():
            raise ConversionError(f"文件不存在: {file_path}")
        
        converter = self.get_converter(file_path)
        if not converter:
            raise ConversionError(f"不支持的文件类型: {file_path.suffix}")
        
        try:
            # 转换文档
            markdown_content = converter.convert_to_markdown(file_path)
            
            # 获取元数据
            metadata = converter.get_metadata(file_path)
            
            return {
                "success": True,
                "markdown_content": markdown_content,
                "metadata": metadata,
                "converter_type": type(converter).__name__
            }
            
        except Exception as e:
            logger.error(f"文件转换失败 {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": str(file_path)
            }
    
    def get_supported_extensions(self) -> List[str]:
        """
        获取所有支持的文件扩展名
        
        Returns:
            支持的文件扩展名列表
        """
        extensions = set()
        for converter in self.converters.values():
            extensions.update(converter.supported_extensions)
        
        return sorted(list(extensions))
    
    def is_supported(self, file_path: Path) -> bool:
        """
        检查文件是否支持转换
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否支持转换
        """
        return self.get_converter(file_path) is not None


# 全局转换器工厂实例
converter_factory = ConverterFactory()
