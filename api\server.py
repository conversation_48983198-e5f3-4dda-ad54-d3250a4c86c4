"""
FastAPI服务器
"""
from fastapi import FastAPI, UploadFile, File, Form, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import tempfile
from pathlib import Path
from typing import List, Optional

from config import settings
from database.models import get_db, init_database, KnowledgeBase, IndustryKnowledgePoint, ExpertTemplate
from database.operations import KnowledgeBaseOperations, KnowledgePointOperations, ExpertTemplateOperations
from core.processor import DocumentProcessor
from core.prompt_generator import ExpertInput
from converters.converter_factory import converter_factory


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.VERSION,
        description="行业知识库AI构建工具API"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 初始化数据库
    init_database()
    
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": f"欢迎使用{settings.APP_NAME}",
            "version": settings.VERSION,
            "docs": "/docs"
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "version": settings.VERSION}
    
    @app.get("/supported-formats")
    async def get_supported_formats():
        """获取支持的文件格式"""
        return {
            "supported_extensions": converter_factory.get_supported_extensions(),
            "max_file_size": settings.MAX_FILE_SIZE
        }
    
    @app.post("/upload-and-process")
    async def upload_and_process(
        file: UploadFile = File(...),
        industry: str = Form(...),
        material_type: str = Form("文档"),
        key_points: str = Form("[]"),
        focus_areas: str = Form("[]"),
        extraction_goals: str = Form("[]"),
        special_requirements: Optional[str] = Form(None),
        db: Session = Depends(get_db)
    ):
        """上传并处理文件"""
        try:
            import json
            
            # 解析JSON字符串
            key_points_list = json.loads(key_points) if key_points else []
            focus_areas_list = json.loads(focus_areas) if focus_areas else []
            extraction_goals_list = json.loads(extraction_goals) if extraction_goals else []
            
            # 检查文件大小
            if file.size > settings.MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大，最大支持 {settings.MAX_FILE_SIZE} 字节"
                )
            
            # 检查文件类型
            file_path = Path(file.filename)
            if not converter_factory.is_supported(file_path):
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件类型: {file_path.suffix}"
                )
            
            # 保存临时文件
            with tempfile.NamedTemporaryFile(
                delete=False,
                suffix=file_path.suffix
            ) as temp_file:
                content = await file.read()
                temp_file.write(content)
                temp_path = Path(temp_file.name)
            
            try:
                # 创建专家输入
                expert_input = ExpertInput(
                    industry=industry,
                    material_type=material_type,
                    key_points=key_points_list,
                    focus_areas=focus_areas_list,
                    extraction_goals=extraction_goals_list,
                    special_requirements=special_requirements
                )
                
                # 处理文件
                processor = DocumentProcessor()
                result = await processor.process_file(
                    temp_path, industry, expert_input=expert_input
                )
                
                if result.success:
                    return {
                        "success": True,
                        "knowledge_base_id": result.knowledge_base_id,
                        "knowledge_points_count": result.knowledge_points_count,
                        "processing_time": result.processing_time,
                        "metadata": result.metadata
                    }
                else:
                    raise HTTPException(
                        status_code=500,
                        detail=result.error_message
                    )
                    
            finally:
                # 清理临时文件
                temp_path.unlink()
                
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=400,
                detail="JSON格式错误"
            )
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=str(e)
            )
    
    @app.get("/knowledge-bases")
    async def list_knowledge_bases(
        industry: Optional[str] = None,
        limit: int = 50,
        db: Session = Depends(get_db)
    ):
        """获取知识库列表"""
        kb_ops = KnowledgeBaseOperations(db)
        
        if industry:
            knowledge_bases = kb_ops.search_knowledge_bases(industry=industry, limit=limit)
        else:
            knowledge_bases = db.query(KnowledgeBase).limit(limit).all()
        
        return {
            "knowledge_bases": [
                {
                    "id": kb.id,
                    "industry": kb.industry,
                    "title": kb.title,
                    "keywords": kb.keywords,
                    "description": kb.description,
                    "knowledge_source": kb.knowledge_source,
                    "created_at": kb.created_at.isoformat(),
                    "updated_at": kb.updated_at.isoformat()
                }
                for kb in knowledge_bases
            ]
        }
    
    @app.get("/knowledge-bases/{kb_id}/points")
    async def get_knowledge_points(
        kb_id: int,
        db: Session = Depends(get_db)
    ):
        """获取知识库的知识点"""
        kp_ops = KnowledgePointOperations(db)
        
        # 获取知识点
        knowledge_points = db.query(IndustryKnowledgePoint).filter(
            IndustryKnowledgePoint.knowledge_base_id == kb_id
        ).all()
        
        return {
            "knowledge_points": [
                {
                    "id": kp.id,
                    "industry": kp.industry,
                    "material_type": kp.material_type,
                    "knowledge_point": kp.knowledge_point,
                    "prompt": kp.prompt,
                    "is_manually_modified": kp.is_manually_modified,
                    "is_active": kp.is_active,
                    "created_at": kp.created_at.isoformat(),
                    "updated_at": kp.updated_at.isoformat()
                }
                for kp in knowledge_points
            ]
        }
    
    @app.put("/knowledge-points/{kp_id}")
    async def update_knowledge_point(
        kp_id: int,
        knowledge_point: Optional[str] = None,
        prompt: Optional[str] = None,
        db: Session = Depends(get_db)
    ):
        """更新知识点"""
        kp_ops = KnowledgePointOperations(db)
        
        updated_kp = kp_ops.update_knowledge_point(
            kp_id=kp_id,
            knowledge_point=knowledge_point,
            prompt=prompt,
            is_manually_modified=True
        )
        
        if not updated_kp:
            raise HTTPException(status_code=404, detail="知识点不存在")
        
        return {
            "success": True,
            "knowledge_point": {
                "id": updated_kp.id,
                "knowledge_point": updated_kp.knowledge_point,
                "prompt": updated_kp.prompt,
                "is_manually_modified": updated_kp.is_manually_modified,
                "updated_at": updated_kp.updated_at.isoformat()
            }
        }
    
    @app.get("/templates")
    async def list_templates(
        industry: Optional[str] = None,
        material_type: Optional[str] = None,
        db: Session = Depends(get_db)
    ):
        """获取专家模板列表"""
        template_ops = ExpertTemplateOperations(db)
        
        if industry:
            templates = template_ops.get_templates_by_industry(
                industry=industry,
                material_type=material_type
            )
        else:
            templates = db.query(ExpertTemplate).filter(
                ExpertTemplate.is_active == True
            ).all()
        
        return {
            "templates": [
                {
                    "id": template.id,
                    "industry": template.industry,
                    "material_type": template.material_type,
                    "template_name": template.template_name,
                    "key_points": template.key_points,
                    "prompt_template": template.prompt_template,
                    "usage_count": template.usage_count,
                    "created_by": template.created_by,
                    "created_at": template.created_at.isoformat()
                }
                for template in templates
            ]
        }
    
    @app.post("/templates")
    async def create_template(
        industry: str = Form(...),
        material_type: str = Form(...),
        template_name: str = Form(...),
        key_points: str = Form(...),
        prompt_template: str = Form(...),
        created_by: Optional[str] = Form(None),
        db: Session = Depends(get_db)
    ):
        """创建专家模板"""
        try:
            import json
            key_points_list = json.loads(key_points)
            
            template_ops = ExpertTemplateOperations(db)
            template = template_ops.create_template(
                industry=industry,
                material_type=material_type,
                template_name=template_name,
                key_points=key_points_list,
                prompt_template=prompt_template,
                created_by=created_by
            )
            
            return {
                "success": True,
                "template_id": template.id,
                "message": "模板创建成功"
            }
            
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="key_points JSON格式错误")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    return app
