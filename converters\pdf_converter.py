"""
PDF文档转换器
"""
import re
from pathlib import Path
from typing import Dict, Any

import PyPDF2
from loguru import logger

from .base import BaseConverter, ConversionError


class PDFConverter(BaseConverter):
    """PDF转换器"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.pdf'}
    
    def convert_to_markdown(self, file_path: Path) -> str:
        """
        将PDF转换为Markdown
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            Markdown内容
        """
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # 提取所有页面的文本
                text_content = []
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content.append(f"## 第 {page_num + 1} 页\n\n{page_text}\n")
                    except Exception as e:
                        logger.warning(f"提取第 {page_num + 1} 页失败: {e}")
                        continue
                
                if not text_content:
                    raise ConversionError("无法从PDF中提取文本内容")
                
                # 合并所有页面内容
                full_text = "\n".join(text_content)
                
                # 清理和格式化文本
                markdown_content = self._clean_and_format_text(full_text)
                
                return markdown_content
                
        except Exception as e:
            logger.error(f"PDF转换失败: {e}")
            raise ConversionError(f"PDF转换失败: {e}")
    
    def _clean_and_format_text(self, text: str) -> str:
        """
        清理和格式化文本
        
        Args:
            text: 原始文本
            
        Returns:
            格式化后的文本
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 处理换行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 尝试识别标题（简单的启发式方法）
        lines = text.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                formatted_lines.append('')
                continue
            
            # 如果行很短且包含常见标题词汇，可能是标题
            if (len(line) < 50 and 
                any(keyword in line for keyword in ['第', '章', '节', '部分', '概述', '介绍', '总结'])):
                formatted_lines.append(f"### {line}")
            else:
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def get_metadata(self, file_path: Path) -> Dict[str, Any]:
        """获取PDF元数据"""
        metadata = super().get_metadata(file_path)
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                metadata.update({
                    "page_count": len(pdf_reader.pages),
                    "pdf_info": pdf_reader.metadata if pdf_reader.metadata else {}
                })
                
        except Exception as e:
            logger.warning(f"获取PDF元数据失败: {e}")
        
        return metadata
