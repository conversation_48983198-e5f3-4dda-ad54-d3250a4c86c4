# 行业知识库AI构建工具 - 项目总结

## 项目概述

本项目是一个完整的行业知识库AI构建工具，旨在帮助企业快速高效地构建行业知识库，满足数据私密性要求。

## 核心特性

### 1. 多格式文档支持
- **PDF文档**: 使用PyPDF2进行文本提取，支持分页处理
- **Word文档**: 支持.docx和.doc格式，保留格式信息
- **HTML文档**: 智能提取主要内容，过滤无关信息
- **图片文档**: 集成OCR技术，支持中英文识别

### 2. 智能知识提取
- **文本分割**: 提供长度、段落、语义三种分割策略
- **AI驱动**: 支持OpenAI和Anthropic两大AI平台
- **专家引导**: 通过专家要点配置优化提取效果
- **模板复用**: 支持行业经验模板的创建和复用

### 3. 大文件处理
- **自动检测**: 超过100MB自动启用大文件处理模式
- **分段策略**: PDF按页面、文本按段落、其他格式先转换后分段
- **并行处理**: 支持多线程并发处理提高效率

### 4. 多种交互方式
- **命令行界面**: 专家友好的交互式界面
- **Web API**: RESTful API支持集成开发
- **批量处理**: 支持多文件批量处理

## 技术架构

### 核心模块

1. **converters/**: 文档转换器
   - `base.py`: 转换器基类
   - `pdf_converter.py`: PDF转换器
   - `word_converter.py`: Word转换器
   - `html_converter.py`: HTML转换器
   - `image_converter.py`: 图片OCR转换器
   - `converter_factory.py`: 转换器工厂

2. **extractors/**: 知识提取器
   - `text_splitter.py`: 文本分割器
   - `knowledge_extractor.py`: 知识点提取器

3. **core/**: 核心处理逻辑
   - `prompt_generator.py`: AI提示词生成器
   - `ai_client.py`: AI客户端封装
   - `processor.py`: 文档处理器
   - `large_file_handler.py`: 大文件处理器

4. **database/**: 数据库层
   - `models.py`: 数据库模型
   - `operations.py`: 数据库操作

5. **cli/**: 命令行界面
   - `expert_interface.py`: 专家交互界面

6. **api/**: Web API
   - `server.py`: FastAPI服务器

### 数据库设计

#### 知识库信息表 (knowledge_bases)
- 存储知识库基本信息
- 支持按行业、关键词搜索

#### 行业资料知识要点表 (industry_knowledge_points)
- 存储提取的知识点
- 支持人工修改标记

#### 专家模板表 (expert_templates)
- 存储专家经验模板
- 支持模板复用和使用统计

## 使用流程

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件配置AI API密钥

# 初始化数据库
python main.py init
```

### 2. 创建专家模板
```bash
# 启动专家界面
python main.py expert
# 选择"创建专家模板"
# 输入行业信息和关键要点
```

### 3. 处理文档
```bash
# 命令行处理
python main.py process document.pdf --industry "科技"

# 或使用专家界面
python main.py expert
# 选择"处理文档"
```

### 4. API服务
```bash
# 启动Web服务
python main.py server

# 使用API上传处理文件
curl -X POST "http://localhost:8000/upload-and-process" \
  -F "file=@document.pdf" \
  -F "industry=科技"
```

## 测试覆盖

项目包含完整的单元测试：

- **test_converters.py**: 文档转换器测试
- **test_extractors.py**: 知识提取器测试  
- **test_database.py**: 数据库操作测试

运行测试：
```bash
pytest
```

## 配置说明

### 环境变量配置
- `OPENAI_API_KEY`: OpenAI API密钥
- `ANTHROPIC_API_KEY`: Anthropic API密钥
- `DATABASE_URL`: 数据库连接字符串
- `MAX_FILE_SIZE`: 最大文件大小限制
- `TESSERACT_CMD`: OCR工具路径

### 专家要点配置
支持JSON格式的专家要点配置，包括：
- 行业类型
- 材料类型
- 关键要点
- 关注领域
- 提取目标
- 特殊要求

## 扩展性

### 添加新的文档转换器
1. 继承`BaseConverter`类
2. 实现`convert_to_markdown`方法
3. 在`ConverterFactory`中注册

### 添加新的AI客户端
1. 继承`BaseAIClient`类
2. 实现必要的方法
3. 在`AIClientFactory`中添加支持

### 添加新的分割策略
1. 继承`BaseSplitter`类
2. 实现`split_text`方法
3. 在`TextSplitterFactory`中注册

## 部署建议

### 开发环境
- 使用SQLite数据库
- 本地文件存储
- 单进程运行

### 生产环境
- 使用PostgreSQL数据库
- 对象存储服务
- 多进程/容器化部署
- 负载均衡
- 监控和日志

## 安全考虑

1. **API安全**: 添加认证和授权机制
2. **文件安全**: 文件类型验证和病毒扫描
3. **数据安全**: 敏感数据加密存储
4. **网络安全**: HTTPS和防火墙配置

## 性能优化

1. **缓存策略**: Redis缓存频繁查询
2. **异步处理**: 大文件异步处理
3. **数据库优化**: 索引和查询优化
4. **资源管理**: 内存和CPU使用监控

## 未来规划

1. **多模态支持**: 视频和音频文件处理
2. **知识图谱**: 构建知识关系网络
3. **智能推荐**: 基于历史数据的智能推荐
4. **可视化界面**: Web前端界面开发
5. **企业集成**: 与企业系统的深度集成

## 总结

本项目成功实现了一个完整的行业知识库AI构建工具，具备以下优势：

- **功能完整**: 覆盖从文档转换到知识提取的完整流程
- **技术先进**: 集成最新的AI技术和最佳实践
- **架构清晰**: 模块化设计，易于维护和扩展
- **用户友好**: 提供多种交互方式，满足不同用户需求
- **质量保证**: 完整的测试覆盖和文档说明

该工具可以帮助企业快速构建高质量的行业知识库，提高知识管理效率，支持业务决策和创新发展。
