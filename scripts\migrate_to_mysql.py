#!/usr/bin/env python3
"""
从SQLite迁移到MySQL的脚本
"""
import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from database.models import SessionLocal, KnowledgeBase, IndustryKnowledgePoint, ExpertTemplate
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import json


def migrate_from_sqlite(sqlite_db_path: str):
    """从SQLite数据库迁移数据到MySQL"""
    
    if not Path(sqlite_db_path).exists():
        print(f"SQLite数据库文件不存在: {sqlite_db_path}")
        return False
    
    try:
        # 连接SQLite数据库
        sqlite_conn = sqlite3.connect(sqlite_db_path)
        sqlite_conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        
        # 连接MySQL数据库
        mysql_session = SessionLocal()
        
        print("开始迁移数据...")
        
        # 迁移知识库数据
        migrate_knowledge_bases(sqlite_conn, mysql_session)
        
        # 迁移知识点数据
        migrate_knowledge_points(sqlite_conn, mysql_session)
        
        # 迁移专家模板数据
        migrate_expert_templates(sqlite_conn, mysql_session)
        
        mysql_session.commit()
        mysql_session.close()
        sqlite_conn.close()
        
        print("数据迁移完成！")
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        if 'mysql_session' in locals():
            mysql_session.rollback()
            mysql_session.close()
        return False


def migrate_knowledge_bases(sqlite_conn, mysql_session):
    """迁移知识库数据"""
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT * FROM knowledge_bases")
    rows = cursor.fetchall()
    
    count = 0
    for row in rows:
        kb = KnowledgeBase(
            id=row['id'],
            industry=row['industry'],
            title=row['title'],
            keywords=row['keywords'],
            description=row['description'],
            knowledge_source=row['knowledge_source'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
        mysql_session.add(kb)
        count += 1
    
    print(f"迁移了 {count} 个知识库记录")


def migrate_knowledge_points(sqlite_conn, mysql_session):
    """迁移知识点数据"""
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT * FROM industry_knowledge_points")
    rows = cursor.fetchall()
    
    count = 0
    for row in rows:
        kp = IndustryKnowledgePoint(
            id=row['id'],
            knowledge_base_id=row['knowledge_base_id'],
            industry=row['industry'],
            material_type=row['material_type'],
            knowledge_point=row['knowledge_point'],
            prompt=row['prompt'],
            is_manually_modified=bool(row['is_manually_modified']),
            is_active=bool(row['is_active']),
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
        mysql_session.add(kp)
        count += 1
    
    print(f"迁移了 {count} 个知识点记录")


def migrate_expert_templates(sqlite_conn, mysql_session):
    """迁移专家模板数据"""
    cursor = sqlite_conn.cursor()
    
    # 检查表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='expert_templates'")
    if not cursor.fetchone():
        print("expert_templates表不存在，跳过迁移")
        return
    
    cursor.execute("SELECT * FROM expert_templates")
    rows = cursor.fetchall()
    
    count = 0
    for row in rows:
        template = ExpertTemplate(
            id=row['id'],
            industry=row['industry'],
            material_type=row['material_type'],
            template_name=row['template_name'],
            key_points=row['key_points'],
            prompt_template=row['prompt_template'],
            usage_count=row['usage_count'],
            is_active=bool(row['is_active']),
            created_by=row['created_by'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
        mysql_session.add(template)
        count += 1
    
    print(f"迁移了 {count} 个专家模板记录")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python migrate_to_mysql.py <sqlite_db_path>")
        print("示例: python migrate_to_mysql.py knowledge_base.db")
        return False
    
    sqlite_db_path = sys.argv[1]
    
    print(f"准备从 {sqlite_db_path} 迁移数据到MySQL...")
    
    # 确认操作
    confirm = input("这将覆盖MySQL数据库中的现有数据，是否继续？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return False
    
    return migrate_from_sqlite(sqlite_db_path)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
