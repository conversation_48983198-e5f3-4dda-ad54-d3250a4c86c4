"""
配置文件
"""
import os
from pathlib import Path
from typing import Optional

from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "行业知识库AI构建工具"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./knowledge_base.db"
    
    # AI配置
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    DEFAULT_AI_MODEL: str = "gpt-3.5-turbo"
    
    # 文件处理配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    CHUNK_SIZE: int = 1000  # 文档分块大小
    CHUNK_OVERLAP: int = 200  # 分块重叠大小
    
    # 存储配置
    UPLOAD_DIR: Path = Path("uploads")
    OUTPUT_DIR: Path = Path("outputs")
    TEMP_DIR: Path = Path("temp")
    
    # OCR配置
    TESSERACT_CMD: Optional[str] = None  # Tesseract可执行文件路径
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
settings = Settings()

# 确保目录存在
for directory in [settings.UPLOAD_DIR, settings.OUTPUT_DIR, settings.TEMP_DIR]:
    directory.mkdir(exist_ok=True)
