"""
文档转换器测试
"""
import pytest
from pathlib import Path
import tempfile

from converters.pdf_converter import PDFConverter
from converters.word_converter import WordConverter
from converters.html_converter import HTMLConverter
from converters.converter_factory import converter_factory


class TestPDFConverter:
    """PDF转换器测试"""
    
    def test_can_convert(self):
        """测试文件类型检查"""
        converter = PDFConverter()
        
        assert converter.can_convert(Path("test.pdf"))
        assert not converter.can_convert(Path("test.docx"))
        assert not converter.can_convert(Path("test.txt"))
    
    def test_get_metadata(self):
        """测试元数据获取"""
        converter = PDFConverter()
        
        # 创建临时PDF文件用于测试
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_path = Path(temp_file.name)
            temp_file.write(b'%PDF-1.4\n')  # 简单的PDF头
        
        try:
            metadata = converter.get_metadata(temp_path)
            
            assert "file_name" in metadata
            assert "file_size" in metadata
            assert "file_extension" in metadata
            assert metadata["file_extension"] == ".pdf"
            
        finally:
            temp_path.unlink()


class TestWordConverter:
    """Word转换器测试"""
    
    def test_can_convert(self):
        """测试文件类型检查"""
        converter = WordConverter()
        
        assert converter.can_convert(Path("test.docx"))
        assert converter.can_convert(Path("test.doc"))
        assert not converter.can_convert(Path("test.pdf"))
        assert not converter.can_convert(Path("test.txt"))


class TestHTMLConverter:
    """HTML转换器测试"""
    
    def test_can_convert(self):
        """测试文件类型检查"""
        converter = HTMLConverter()
        
        assert converter.can_convert(Path("test.html"))
        assert converter.can_convert(Path("test.htm"))
        assert not converter.can_convert(Path("test.pdf"))
        assert not converter.can_convert(Path("test.txt"))
    
    def test_convert_simple_html(self):
        """测试简单HTML转换"""
        converter = HTMLConverter()
        
        # 创建临时HTML文件
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>测试文档</title>
        </head>
        <body>
            <h1>标题1</h1>
            <p>这是一个段落。</p>
            <h2>标题2</h2>
            <p>这是另一个段落。</p>
        </body>
        </html>
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(html_content)
            temp_path = Path(temp_file.name)
        
        try:
            markdown = converter.convert_to_markdown(temp_path)
            
            assert "# 标题1" in markdown
            assert "## 标题2" in markdown
            assert "这是一个段落" in markdown
            assert "这是另一个段落" in markdown
            
        finally:
            temp_path.unlink()


class TestConverterFactory:
    """转换器工厂测试"""
    
    def test_get_converter(self):
        """测试获取转换器"""
        factory = converter_factory
        
        # 测试PDF
        pdf_converter = factory.get_converter(Path("test.pdf"))
        assert isinstance(pdf_converter, PDFConverter)
        
        # 测试Word
        word_converter = factory.get_converter(Path("test.docx"))
        assert isinstance(word_converter, WordConverter)
        
        # 测试HTML
        html_converter = factory.get_converter(Path("test.html"))
        assert isinstance(html_converter, HTMLConverter)
        
        # 测试不支持的类型
        unsupported_converter = factory.get_converter(Path("test.xyz"))
        assert unsupported_converter is None
    
    def test_is_supported(self):
        """测试文件支持检查"""
        factory = converter_factory
        
        assert factory.is_supported(Path("test.pdf"))
        assert factory.is_supported(Path("test.docx"))
        assert factory.is_supported(Path("test.html"))
        assert not factory.is_supported(Path("test.xyz"))
    
    def test_get_supported_extensions(self):
        """测试获取支持的扩展名"""
        factory = converter_factory
        extensions = factory.get_supported_extensions()
        
        assert ".pdf" in extensions
        assert ".docx" in extensions
        assert ".html" in extensions
        assert ".htm" in extensions
