# 行业知识库AI构建工具

一个快速高效的企业行业知识库构建工具，支持多种文档格式，通过AI技术自动提取和组织知识点。

## 特性

- 🚀 **多格式支持**: 支持PDF、Word、HTML、图片等多种文档格式
- 🧠 **AI驱动**: 基于大语言模型的智能知识提取
- 👨‍💼 **专家友好**: 直观的专家交互界面，快速配置提取要点
- 📊 **模板复用**: 支持行业经验模板的创建和复用
- 🔄 **批量处理**: 支持大文件和批量文件处理
- 🛡️ **数据私密**: 本地部署，保护数据隐私
- 🌐 **多接口**: 提供命令行、Web API等多种使用方式

## 快速开始

### 环境要求

- Python 3.8+
- 支持的操作系统: Windows, macOS, Linux

### 安装

1. 克隆项目
```bash
git clone <repository-url>
cd knowledge_base_construct_tools
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置AI API密钥
```

4. 初始化数据库
```bash
python main.py init
```

### 基本使用

#### 1. 命令行界面

启动专家交互界面：
```bash
python main.py expert
```

#### 2. 处理单个文件

```bash
python main.py process document.pdf --industry "科技" --expert-points expert_config.json
```

#### 3. Web API服务

启动Web服务器：
```bash
python main.py server --host 0.0.0.0 --port 8000
```

访问 http://localhost:8000/docs 查看API文档。

## 配置说明

### 环境变量

在 `.env` 文件中配置以下变量：

```env
# 数据库配置
DATABASE_URL=sqlite:///./knowledge_base.db

# AI API配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_AI_MODEL=gpt-3.5-turbo

# 文件处理配置
MAX_FILE_SIZE=104857600  # 100MB
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# OCR配置（可选）
TESSERACT_CMD=/usr/bin/tesseract
```

### 专家要点配置

创建JSON文件定义专家要点：

```json
{
  "industry": "金融",
  "material_type": "研究报告",
  "key_points": [
    "市场分析",
    "风险评估",
    "投资建议"
  ],
  "focus_areas": [
    "宏观经济",
    "行业趋势",
    "公司基本面"
  ],
  "extraction_goals": [
    "提取投资机会",
    "识别风险因素",
    "总结核心观点"
  ],
  "special_requirements": "重点关注数据和图表信息"
}
```

## 使用指南

### 1. 创建专家模板

通过专家界面创建可复用的行业模板：

1. 启动专家界面: `python main.py expert`
2. 选择"创建专家模板"
3. 输入行业信息和关键要点
4. 系统自动生成prompt模板
5. 确认保存模板

### 2. 处理文档

#### 支持的文件格式

- **PDF文件**: `.pdf`
- **Word文档**: `.docx`, `.doc`
- **HTML文件**: `.html`, `.htm`
- **图片文件**: `.png`, `.jpg`, `.jpeg`, `.tiff`, `.bmp`, `.gif` (需要OCR)

#### 处理流程

1. **文档转换**: 将各种格式转换为Markdown
2. **文本分割**: 根据内容特点智能分段
3. **知识提取**: 使用AI提取关键信息
4. **结果存储**: 保存到数据库供后续使用

### 3. 大文件处理

系统自动检测文件大小，对于超过100MB的文件：

- **PDF**: 按页面分批处理
- **文本**: 按段落智能分割
- **其他格式**: 先转换后分段处理

### 4. API使用

#### 上传并处理文件

```bash
curl -X POST "http://localhost:8000/upload-and-process" \
  -F "file=@document.pdf" \
  -F "industry=科技" \
  -F "material_type=技术文档" \
  -F "key_points=[\"核心技术\", \"实现方案\"]"
```

#### 获取知识库列表

```bash
curl "http://localhost:8000/knowledge-bases?industry=科技"
```

#### 获取知识点

```bash
curl "http://localhost:8000/knowledge-bases/1/points"
```

## 架构说明

### 核心模块

- **converters/**: 文档转换器，支持多种格式
- **extractors/**: 知识提取器，包含文本分割和AI提取
- **core/**: 核心处理逻辑，包含prompt生成和AI客户端
- **database/**: 数据库模型和操作
- **cli/**: 命令行界面
- **api/**: Web API服务

### 数据库设计

#### 知识库信息表 (knowledge_bases)
- id: 主键
- industry: 行业
- title: 标题
- keywords: 关键词(JSON)
- description: 描述
- knowledge_source: 知识来源

#### 行业资料知识要点表 (industry_knowledge_points)
- id: 主键
- knowledge_base_id: 关联知识库
- industry: 行业
- material_type: 资料类型
- knowledge_point: 知识要点
- prompt: 提取prompt
- is_manually_modified: 是否人工修改
- is_active: 当前可用

#### 专家模板表 (expert_templates)
- id: 主键
- industry: 行业
- material_type: 资料类型
- template_name: 模板名称
- key_points: 关键要点(JSON)
- prompt_template: prompt模板
- usage_count: 使用次数

## 开发指南

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_converters.py

# 运行测试并显示覆盖率
pytest --cov=.
```

### 添加新的文档转换器

1. 继承 `BaseConverter` 类
2. 实现 `convert_to_markdown` 方法
3. 在 `ConverterFactory` 中注册

### 扩展AI客户端

1. 继承 `BaseAIClient` 类
2. 实现必要的方法
3. 在 `AIClientFactory` 中添加支持

## 常见问题

### Q: 如何处理中文文档？
A: 系统原生支持中文，OCR组件也配置了中文识别。

### Q: 可以离线使用吗？
A: 需要AI API进行知识提取，但可以使用本地部署的大模型。

### Q: 如何提高提取准确性？
A: 1) 优化专家要点配置 2) 使用更强的AI模型 3) 人工审核和修正

### Q: 支持哪些AI模型？
A: 目前支持OpenAI GPT系列和Anthropic Claude系列，可扩展其他模型。

## 许可证

[MIT License](LICENSE)

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请通过以下方式联系：
- 提交Issue
- 发送邮件至: [<EMAIL>]