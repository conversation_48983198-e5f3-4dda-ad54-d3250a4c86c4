#!/usr/bin/env python3
"""
快速启动脚本
"""
import sys
import subprocess
from pathlib import Path


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python run.py init          # 初始化数据库")
        print("  python run.py expert        # 启动专家界面")
        print("  python run.py server        # 启动Web服务器")
        print("  python run.py test          # 运行测试")
        print("  python run.py install       # 安装依赖")
        return
    
    command = sys.argv[1]
    
    if command == "init":
        subprocess.run([sys.executable, "main.py", "init"])
    elif command == "expert":
        subprocess.run([sys.executable, "main.py", "expert"])
    elif command == "server":
        subprocess.run([sys.executable, "main.py", "server"])
    elif command == "test":
        subprocess.run([sys.executable, "-m", "pytest"])
    elif command == "install":
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    else:
        print(f"未知命令: {command}")


if __name__ == "__main__":
    main()
