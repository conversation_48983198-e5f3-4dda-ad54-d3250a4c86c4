"""
Word文档转换器
"""
import re
from pathlib import Path
from typing import Dict, Any

from docx import Document
from loguru import logger

from .base import BaseConverter, ConversionError


class WordConverter(BaseConverter):
    """Word文档转换器"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.docx', '.doc'}
    
    def convert_to_markdown(self, file_path: Path) -> str:
        """
        将Word文档转换为Markdown
        
        Args:
            file_path: Word文件路径
            
        Returns:
            Markdown内容
        """
        try:
            doc = Document(file_path)
            
            markdown_content = []
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if not text:
                    markdown_content.append('')
                    continue
                
                # 根据样式判断标题级别
                style_name = paragraph.style.name.lower()
                
                if 'heading 1' in style_name or 'title' in style_name:
                    markdown_content.append(f"# {text}")
                elif 'heading 2' in style_name:
                    markdown_content.append(f"## {text}")
                elif 'heading 3' in style_name:
                    markdown_content.append(f"### {text}")
                elif 'heading 4' in style_name:
                    markdown_content.append(f"#### {text}")
                elif 'heading 5' in style_name:
                    markdown_content.append(f"##### {text}")
                elif 'heading 6' in style_name:
                    markdown_content.append(f"###### {text}")
                else:
                    # 处理普通段落
                    formatted_text = self._format_paragraph_text(paragraph)
                    markdown_content.append(formatted_text)
            
            # 处理表格
            for table in doc.tables:
                table_md = self._convert_table_to_markdown(table)
                if table_md:
                    markdown_content.append(table_md)
            
            return '\n'.join(markdown_content)
            
        except Exception as e:
            logger.error(f"Word文档转换失败: {e}")
            raise ConversionError(f"Word文档转换失败: {e}")
    
    def _format_paragraph_text(self, paragraph) -> str:
        """
        格式化段落文本，处理粗体、斜体等格式
        
        Args:
            paragraph: Word段落对象
            
        Returns:
            格式化后的Markdown文本
        """
        text_parts = []
        
        for run in paragraph.runs:
            text = run.text
            if not text:
                continue
            
            # 处理粗体
            if run.bold:
                text = f"**{text}**"
            
            # 处理斜体
            if run.italic:
                text = f"*{text}*"
            
            text_parts.append(text)
        
        return ''.join(text_parts)
    
    def _convert_table_to_markdown(self, table) -> str:
        """
        将Word表格转换为Markdown表格
        
        Args:
            table: Word表格对象
            
        Returns:
            Markdown表格字符串
        """
        try:
            rows = []
            
            for row_idx, row in enumerate(table.rows):
                cells = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    cells.append(cell_text)
                
                rows.append('| ' + ' | '.join(cells) + ' |')
                
                # 添加表头分隔符
                if row_idx == 0:
                    separator = '| ' + ' | '.join(['---'] * len(cells)) + ' |'
                    rows.append(separator)
            
            return '\n'.join(rows) + '\n'
            
        except Exception as e:
            logger.warning(f"表格转换失败: {e}")
            return ""
    
    def get_metadata(self, file_path: Path) -> Dict[str, Any]:
        """获取Word文档元数据"""
        metadata = super().get_metadata(file_path)
        
        try:
            doc = Document(file_path)
            
            # 获取文档属性
            core_props = doc.core_properties
            
            metadata.update({
                "title": core_props.title or "",
                "author": core_props.author or "",
                "subject": core_props.subject or "",
                "created": core_props.created.isoformat() if core_props.created else None,
                "modified": core_props.modified.isoformat() if core_props.modified else None,
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables)
            })
            
        except Exception as e:
            logger.warning(f"获取Word元数据失败: {e}")
        
        return metadata
