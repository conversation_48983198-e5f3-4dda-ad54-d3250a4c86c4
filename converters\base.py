"""
文档转换基类
"""
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional, Dict, Any


class BaseConverter(ABC):
    """文档转换器基类"""
    
    def __init__(self):
        self.supported_extensions = set()
    
    @abstractmethod
    def convert_to_markdown(self, file_path: Path) -> str:
        """
        将文档转换为Markdown格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            转换后的Markdown内容
        """
        pass
    
    def can_convert(self, file_path: Path) -> bool:
        """
        检查是否支持转换该文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否支持转换
        """
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_metadata(self, file_path: Path) -> Dict[str, Any]:
        """
        获取文档元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            文档元数据
        """
        return {
            "file_name": file_path.name,
            "file_size": file_path.stat().st_size,
            "file_extension": file_path.suffix.lower(),
            "file_path": str(file_path)
        }


class ConversionError(Exception):
    """转换错误异常"""
    pass
