"""
文档处理器
"""
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from loguru import logger

from config import settings
from database.models import SessionLocal
from database.operations import KnowledgeBaseOperations, KnowledgePointOperations
from converters.converter_factory import converter_factory
from extractors.knowledge_extractor import KnowledgeExtractor
from core.prompt_generator import ExpertInput
from .large_file_handler import LargeFileHandler


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    knowledge_base_id: Optional[int] = None
    knowledge_points_count: int = 0
    processing_time: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self):
        self.extractor = KnowledgeExtractor()
        self.large_file_handler = LargeFileHandler()
    
    async def process_file(
        self,
        file_path: Path,
        industry: str,
        expert_points_file: str = None,
        expert_input: ExpertInput = None
    ) -> ProcessingResult:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            industry: 行业
            expert_points_file: 专家要点文件路径
            expert_input: 专家输入对象
            
        Returns:
            处理结果
        """
        import time
        start_time = time.time()
        
        try:
            # 检查文件是否存在
            if not file_path.exists():
                return ProcessingResult(
                    success=False,
                    error_message=f"文件不存在: {file_path}"
                )
            
            # 检查文件大小
            file_size = file_path.stat().st_size
            if file_size > settings.MAX_FILE_SIZE:
                logger.info(f"文件过大 ({file_size} bytes)，使用大文件处理器")
                return await self._process_large_file(
                    file_path, industry, expert_input
                )
            
            # 转换文档
            logger.info(f"开始转换文档: {file_path}")
            conversion_result = converter_factory.convert_file(file_path)
            
            if not conversion_result["success"]:
                return ProcessingResult(
                    success=False,
                    error_message=f"文档转换失败: {conversion_result['error']}"
                )
            
            markdown_content = conversion_result["markdown_content"]
            metadata = conversion_result["metadata"]
            
            # 获取专家输入
            if not expert_input:
                expert_input = await self._load_expert_input(
                    expert_points_file, industry
                )
            
            # 提取标题和关键词
            title_keywords = await self.extractor.extract_title_and_keywords(
                markdown_content, expert_input
            )
            
            # 创建知识库记录
            db = SessionLocal()
            try:
                kb_ops = KnowledgeBaseOperations(db)
                knowledge_base = kb_ops.create_knowledge_base(
                    industry=industry,
                    title=title_keywords.get("title", file_path.name),
                    keywords=title_keywords.get("keywords", []),
                    description=f"从文件 {file_path.name} 提取的知识",
                    knowledge_source=str(file_path)
                )
                
                # 提取知识点
                logger.info("开始提取知识点")
                extraction_result = await self.extractor.extract_knowledge_points(
                    markdown_content, expert_input
                )
                
                # 保存知识点
                kp_ops = KnowledgePointOperations(db)
                saved_count = 0
                
                for point in extraction_result.knowledge_points:
                    try:
                        kp_ops.create_knowledge_point(
                            knowledge_base_id=knowledge_base.id,
                            industry=industry,
                            material_type=expert_input.material_type,
                            knowledge_point=point.content,
                            prompt=json.dumps({
                                "title": point.title,
                                "keywords": point.keywords,
                                "summary": point.summary,
                                "industry_info": point.industry_info,
                                "confidence": point.confidence
                            }, ensure_ascii=False)
                        )
                        saved_count += 1
                    except Exception as e:
                        logger.warning(f"保存知识点失败: {e}")
                
                processing_time = time.time() - start_time
                
                return ProcessingResult(
                    success=True,
                    knowledge_base_id=knowledge_base.id,
                    knowledge_points_count=saved_count,
                    processing_time=processing_time,
                    metadata={
                        "file_metadata": metadata,
                        "extraction_stats": {
                            "total_chunks": extraction_result.total_chunks,
                            "successful_extractions": extraction_result.successful_extractions,
                            "failed_extractions": extraction_result.failed_extractions
                        }
                    }
                )
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    async def _process_large_file(
        self,
        file_path: Path,
        industry: str,
        expert_input: ExpertInput
    ) -> ProcessingResult:
        """
        处理大文件
        
        Args:
            file_path: 文件路径
            industry: 行业
            expert_input: 专家输入
            
        Returns:
            处理结果
        """
        try:
            # 使用大文件处理器
            processing_result = await self.large_file_handler.process_large_file(
                file_path, industry, expert_input
            )
            
            return processing_result
            
        except Exception as e:
            logger.error(f"大文件处理失败: {e}")
            return ProcessingResult(
                success=False,
                error_message=f"大文件处理失败: {e}"
            )
    
    async def _load_expert_input(
        self,
        expert_points_file: str,
        industry: str
    ) -> ExpertInput:
        """
        加载专家输入
        
        Args:
            expert_points_file: 专家要点文件路径
            industry: 行业
            
        Returns:
            专家输入对象
        """
        if expert_points_file and Path(expert_points_file).exists():
            try:
                with open(expert_points_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                return ExpertInput(
                    industry=data.get("industry", industry),
                    material_type=data.get("material_type", "文档"),
                    key_points=data.get("key_points", []),
                    focus_areas=data.get("focus_areas", []),
                    extraction_goals=data.get("extraction_goals", []),
                    special_requirements=data.get("special_requirements")
                )
                
            except Exception as e:
                logger.warning(f"加载专家要点文件失败: {e}")
        
        # 返回默认专家输入
        return ExpertInput(
            industry=industry,
            material_type="文档",
            key_points=["核心概念", "关键信息", "重要数据"],
            focus_areas=["主要内容", "技术要点", "业务逻辑"],
            extraction_goals=["提取关键信息", "识别重要概念", "总结核心内容"]
        )
    
    async def process_batch(
        self,
        file_paths: List[Path],
        industry: str,
        expert_input: ExpertInput = None,
        max_concurrent: int = 3
    ) -> List[ProcessingResult]:
        """
        批量处理文件
        
        Args:
            file_paths: 文件路径列表
            industry: 行业
            expert_input: 专家输入
            max_concurrent: 最大并发数
            
        Returns:
            处理结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_file(file_path: Path) -> ProcessingResult:
            async with semaphore:
                return await self.process_file(file_path, industry, expert_input=expert_input)
        
        tasks = [process_single_file(fp) for fp in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(ProcessingResult(
                    success=False,
                    error_message=f"处理文件 {file_paths[i]} 时发生异常: {result}"
                ))
            else:
                processed_results.append(result)
        
        return processed_results
