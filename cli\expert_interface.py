"""
专家交互界面
"""
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from loguru import logger

from database.models import SessionLocal
from database.operations import ExpertTemplateOperations, KnowledgePointOperations
from core.prompt_generator import ExpertInput
from extractors.knowledge_extractor import KnowledgeExtractor
from converters.converter_factory import converter_factory


class ExpertInterface:
    """专家交互界面"""
    
    def __init__(self):
        self.console = Console()
        self.db = SessionLocal()
        self.template_ops = ExpertTemplateOperations(self.db)
        self.knowledge_ops = KnowledgePointOperations(self.db)
        self.extractor = KnowledgeExtractor()
    
    async def run(self):
        """运行专家界面"""
        try:
            self.console.print(Panel("欢迎使用行业知识库AI构建工具", style="bold blue"))
            
            while True:
                choice = self._show_main_menu()
                
                if choice == "1":
                    await self._create_expert_template()
                elif choice == "2":
                    await self._process_document()
                elif choice == "3":
                    self._view_templates()
                elif choice == "4":
                    self._view_knowledge_points()
                elif choice == "5":
                    await self._modify_knowledge_point()
                elif choice == "6":
                    break
                else:
                    self.console.print("无效选择，请重试", style="red")
        
        finally:
            self.db.close()
    
    def _show_main_menu(self) -> str:
        """显示主菜单"""
        self.console.print("\n" + "="*50)
        self.console.print("主菜单", style="bold")
        self.console.print("1. 创建专家模板")
        self.console.print("2. 处理文档")
        self.console.print("3. 查看模板")
        self.console.print("4. 查看知识点")
        self.console.print("5. 修改知识点")
        self.console.print("6. 退出")
        
        return Prompt.ask("请选择操作", choices=["1", "2", "3", "4", "5", "6"])
    
    async def _create_expert_template(self):
        """创建专家模板"""
        self.console.print(Panel("创建专家模板", style="blue"))
        
        # 收集基本信息
        industry = Prompt.ask("请输入行业")
        material_type = Prompt.ask("请输入资料类型")
        template_name = Prompt.ask("请输入模板名称")
        
        # 收集关键要点
        key_points = self._collect_list_input("关键要点")
        focus_areas = self._collect_list_input("关注领域")
        extraction_goals = self._collect_list_input("提取目标")
        
        # 特殊要求
        special_requirements = Prompt.ask("特殊要求（可选）", default="")
        
        # 创建专家输入对象
        expert_input = ExpertInput(
            industry=industry,
            material_type=material_type,
            key_points=key_points,
            focus_areas=focus_areas,
            extraction_goals=extraction_goals,
            special_requirements=special_requirements if special_requirements else None
        )
        
        # 生成prompt模板
        sample_content = "这是一个示例内容，用于生成prompt模板。"
        prompt_template = self.extractor.prompt_generator.generate_extraction_prompt(
            expert_input, sample_content
        )
        
        # 显示生成的prompt
        self.console.print(Panel("生成的Prompt模板", style="green"))
        syntax = Syntax(prompt_template, "text", theme="monokai", line_numbers=True)
        self.console.print(syntax)
        
        # 确认保存
        if Confirm.ask("是否保存此模板？"):
            try:
                template = self.template_ops.create_template(
                    industry=industry,
                    material_type=material_type,
                    template_name=template_name,
                    key_points=key_points,
                    prompt_template=prompt_template,
                    created_by="专家界面"
                )
                self.console.print(f"模板已保存，ID: {template.id}", style="green")
            except Exception as e:
                self.console.print(f"保存失败: {e}", style="red")
    
    def _collect_list_input(self, item_name: str) -> List[str]:
        """收集列表输入"""
        items = []
        self.console.print(f"\n请输入{item_name}（输入空行结束）:")
        
        while True:
            item = Prompt.ask(f"{item_name} {len(items) + 1}", default="")
            if not item:
                break
            items.append(item)
        
        return items
    
    async def _process_document(self):
        """处理文档"""
        self.console.print(Panel("处理文档", style="blue"))
        
        # 选择文件
        file_path = Prompt.ask("请输入文件路径")
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.console.print("文件不存在", style="red")
            return
        
        # 检查文件类型
        if not converter_factory.is_supported(file_path):
            self.console.print(f"不支持的文件类型: {file_path.suffix}", style="red")
            self.console.print(f"支持的类型: {converter_factory.get_supported_extensions()}")
            return
        
        # 选择或创建专家输入
        expert_input = await self._get_expert_input()
        if not expert_input:
            return
        
        # 转换文档
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("转换文档...", total=None)
            
            try:
                conversion_result = converter_factory.convert_file(file_path)
                if not conversion_result["success"]:
                    self.console.print(f"文档转换失败: {conversion_result['error']}", style="red")
                    return
                
                markdown_content = conversion_result["markdown_content"]
                progress.update(task, description="提取知识点...")
                
                # 提取知识点
                extraction_result = await self.extractor.extract_knowledge_points(
                    markdown_content, expert_input
                )
                
                progress.update(task, description="完成")
                
            except Exception as e:
                self.console.print(f"处理失败: {e}", style="red")
                return
        
        # 显示结果
        self._display_extraction_result(extraction_result)
        
        # 保存结果
        if Confirm.ask("是否保存提取的知识点？"):
            await self._save_knowledge_points(extraction_result, expert_input)
    
    async def _get_expert_input(self) -> Optional[ExpertInput]:
        """获取专家输入"""
        choice = Prompt.ask(
            "选择专家输入方式",
            choices=["1", "2"],
            default="1"
        )
        
        if choice == "1":
            # 使用现有模板
            return self._select_template()
        else:
            # 手动输入
            return self._manual_expert_input()
    
    def _select_template(self) -> Optional[ExpertInput]:
        """选择模板"""
        # 获取所有行业
        industries = self.db.execute(
            "SELECT DISTINCT industry FROM expert_templates WHERE is_active = 1"
        ).fetchall()
        
        if not industries:
            self.console.print("没有可用的模板", style="yellow")
            return None
        
        # 选择行业
        industry_choices = [row[0] for row in industries]
        industry = Prompt.ask("选择行业", choices=industry_choices)
        
        # 获取该行业的模板
        templates = self.template_ops.get_templates_by_industry(industry)
        
        if not templates:
            self.console.print("该行业没有可用模板", style="yellow")
            return None
        
        # 显示模板列表
        table = Table(title="可用模板")
        table.add_column("ID", style="cyan")
        table.add_column("名称", style="green")
        table.add_column("资料类型", style="yellow")
        table.add_column("使用次数", style="blue")
        
        for template in templates:
            table.add_row(
                str(template.id),
                template.template_name,
                template.material_type,
                str(template.usage_count)
            )
        
        self.console.print(table)
        
        # 选择模板
        template_id = int(Prompt.ask("选择模板ID"))
        selected_template = next((t for t in templates if t.id == template_id), None)
        
        if not selected_template:
            self.console.print("无效的模板ID", style="red")
            return None
        
        # 增加使用次数
        self.template_ops.increment_usage(template_id)
        
        # 解析模板数据
        key_points = json.loads(selected_template.key_points)
        
        return ExpertInput(
            industry=selected_template.industry,
            material_type=selected_template.material_type,
            key_points=key_points,
            focus_areas=[],  # 模板中可能没有这些字段
            extraction_goals=[]
        )
    
    def _manual_expert_input(self) -> ExpertInput:
        """手动输入专家信息"""
        industry = Prompt.ask("请输入行业")
        material_type = Prompt.ask("请输入资料类型")
        key_points = self._collect_list_input("关键要点")
        focus_areas = self._collect_list_input("关注领域")
        extraction_goals = self._collect_list_input("提取目标")
        
        return ExpertInput(
            industry=industry,
            material_type=material_type,
            key_points=key_points,
            focus_areas=focus_areas,
            extraction_goals=extraction_goals
        )
    
    def _display_extraction_result(self, result):
        """显示提取结果"""
        self.console.print(Panel("提取结果", style="green"))
        
        # 统计信息
        stats_table = Table(title="统计信息")
        stats_table.add_column("项目", style="cyan")
        stats_table.add_column("数值", style="green")
        
        stats_table.add_row("总块数", str(result.total_chunks))
        stats_table.add_row("成功提取", str(result.successful_extractions))
        stats_table.add_row("失败提取", str(result.failed_extractions))
        stats_table.add_row("处理时间", f"{result.processing_time:.2f}秒")
        
        self.console.print(stats_table)
        
        # 知识点列表
        if result.knowledge_points:
            points_table = Table(title="提取的知识点")
            points_table.add_column("标题", style="cyan")
            points_table.add_column("关键词", style="yellow")
            points_table.add_column("置信度", style="green")
            
            for point in result.knowledge_points[:10]:  # 只显示前10个
                keywords_str = ", ".join(point.keywords[:3])  # 只显示前3个关键词
                points_table.add_row(
                    point.title[:50] + "..." if len(point.title) > 50 else point.title,
                    keywords_str,
                    f"{point.confidence:.2f}"
                )
            
            self.console.print(points_table)
            
            if len(result.knowledge_points) > 10:
                self.console.print(f"... 还有 {len(result.knowledge_points) - 10} 个知识点")
    
    async def _save_knowledge_points(self, result, expert_input):
        """保存知识点"""
        try:
            for point in result.knowledge_points:
                self.knowledge_ops.create_knowledge_point(
                    industry=expert_input.industry,
                    material_type=expert_input.material_type,
                    knowledge_point=point.content,
                    prompt=json.dumps({
                        "title": point.title,
                        "keywords": point.keywords,
                        "summary": point.summary,
                        "industry_info": point.industry_info
                    }, ensure_ascii=False)
                )
            
            self.console.print(f"已保存 {len(result.knowledge_points)} 个知识点", style="green")
            
        except Exception as e:
            self.console.print(f"保存失败: {e}", style="red")
    
    def _view_templates(self):
        """查看模板"""
        # 实现查看模板的逻辑
        pass
    
    def _view_knowledge_points(self):
        """查看知识点"""
        # 实现查看知识点的逻辑
        pass
    
    async def _modify_knowledge_point(self):
        """修改知识点"""
        # 实现修改知识点的逻辑
        pass
