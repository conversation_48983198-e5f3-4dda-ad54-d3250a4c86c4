"""
知识提取器测试
"""
import pytest
from unittest.mock import Mock, AsyncMock

from extractors.text_splitter import LengthBasedSplitter, ParagraphBasedSplitter, SemanticSplitter
from extractors.knowledge_extractor import KnowledgeExtractor
from core.prompt_generator import ExpertInput


class TestTextSplitters:
    """文本分割器测试"""
    
    def test_length_based_splitter(self):
        """测试基于长度的分割器"""
        splitter = LengthBasedSplitter(chunk_size=50, chunk_overlap=10)
        
        text = "这是一个很长的文本。" * 20  # 创建长文本
        chunks = splitter.split_text(text)
        
        assert len(chunks) > 1
        assert all(len(chunk.content) <= 60 for chunk in chunks)  # 允许一些误差
        assert chunks[0].start_index == 0
    
    def test_paragraph_based_splitter(self):
        """测试基于段落的分割器"""
        splitter = ParagraphBasedSplitter(chunk_size=100, chunk_overlap=20)
        
        text = """第一段内容。
这是第一段的详细内容。

第二段内容。
这是第二段的详细内容。

第三段内容。
这是第三段的详细内容。"""
        
        chunks = splitter.split_text(text)
        
        assert len(chunks) >= 1
        assert all(chunk.content.strip() for chunk in chunks)
    
    def test_semantic_splitter(self):
        """测试基于语义的分割器"""
        splitter = SemanticSplitter(chunk_size=200, chunk_overlap=50)
        
        text = """# 第一章 介绍
这是第一章的内容。

## 1.1 背景
这是背景介绍。

# 第二章 方法
这是第二章的内容。

## 2.1 实现
这是实现细节。"""
        
        chunks = splitter.split_text(text)
        
        assert len(chunks) >= 1
        # 检查是否正确识别了章节
        assert any("第一章" in chunk.content for chunk in chunks)
        assert any("第二章" in chunk.content for chunk in chunks)


class TestKnowledgeExtractor:
    """知识提取器测试"""
    
    @pytest.fixture
    def mock_ai_client(self):
        """模拟AI客户端"""
        client = Mock()
        client.extract_json = AsyncMock(return_value={
            "title": "测试标题",
            "keywords": ["关键词1", "关键词2"],
            "summary": "这是一个测试摘要",
            "key_insights": "这是核心洞察",
            "industry_specific_info": "行业特定信息",
            "actionable_items": ["行动项1", "行动项2"]
        })
        client.generate_response = AsyncMock(return_value="生成的标题")
        return client
    
    @pytest.fixture
    def expert_input(self):
        """专家输入示例"""
        return ExpertInput(
            industry="科技",
            material_type="技术文档",
            key_points=["核心技术", "实现方案", "性能指标"],
            focus_areas=["技术架构", "系统设计"],
            extraction_goals=["提取技术要点", "识别关键指标"]
        )
    
    @pytest.mark.asyncio
    async def test_extract_knowledge_points(self, mock_ai_client, expert_input):
        """测试知识点提取"""
        extractor = KnowledgeExtractor(ai_client=mock_ai_client)
        
        text = """这是一个技术文档的示例内容。
        
包含了多个技术要点和实现细节。
系统采用了先进的架构设计。
性能指标达到了预期目标。

详细的实现方案如下：
1. 模块化设计
2. 高性能优化
3. 可扩展架构"""
        
        result = await extractor.extract_knowledge_points(text, expert_input)
        
        assert result.total_chunks > 0
        assert result.successful_extractions >= 0
        assert len(result.knowledge_points) >= 0
        assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_extract_title_and_keywords(self, mock_ai_client, expert_input):
        """测试标题和关键词提取"""
        extractor = KnowledgeExtractor(ai_client=mock_ai_client)
        
        text = "这是一个关于人工智能技术的文档，介绍了机器学习和深度学习的应用。"
        
        result = await extractor.extract_title_and_keywords(text, expert_input)
        
        assert result["success"] is True
        assert "title" in result
        assert "keywords" in result
        assert isinstance(result["keywords"], list)


class TestPromptGenerator:
    """提示词生成器测试"""
    
    def test_generate_extraction_prompt(self):
        """测试提取prompt生成"""
        from core.prompt_generator import PromptGenerator
        
        generator = PromptGenerator()
        expert_input = ExpertInput(
            industry="金融",
            material_type="研究报告",
            key_points=["市场分析", "风险评估"],
            focus_areas=["投资策略", "市场趋势"],
            extraction_goals=["提取投资建议", "识别风险因素"]
        )
        
        content = "这是一份金融研究报告的内容。"
        prompt = generator.generate_extraction_prompt(expert_input, content)
        
        assert "金融" in prompt
        assert "研究报告" in prompt
        assert "市场分析" in prompt
        assert "风险评估" in prompt
        assert content in prompt
    
    def test_generate_title_prompt(self):
        """测试标题prompt生成"""
        from core.prompt_generator import PromptGenerator
        
        generator = PromptGenerator()
        expert_input = ExpertInput(
            industry="医疗",
            material_type="临床报告",
            key_points=["诊断结果", "治疗方案"],
            focus_areas=["病理分析"],
            extraction_goals=["提取诊断信息"]
        )
        
        content = "这是一份临床诊断报告。"
        prompt = generator.generate_title_prompt(expert_input, content)
        
        assert "医疗" in prompt
        assert "临床报告" in prompt
        assert "标题" in prompt
        assert content in prompt
