"""
图片文档转换器（OCR）
"""
import re
from pathlib import Path
from typing import Dict, Any, Optional

from PIL import Image
import pytesseract
from loguru import logger

from config import settings
from .base import BaseConverter, ConversionError


class ImageConverter(BaseConverter):
    """图片转换器（使用OCR）"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'}
        
        # 设置Tesseract路径
        if settings.TESSERACT_CMD:
            pytesseract.pytesseract.tesseract_cmd = settings.TESSERACT_CMD
    
    def convert_to_markdown(self, file_path: Path) -> str:
        """
        使用OCR将图片转换为Markdown
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            Markdown内容
        """
        try:
            # 打开图片
            image = Image.open(file_path)
            
            # 预处理图片以提高OCR准确性
            processed_image = self._preprocess_image(image)
            
            # 执行OCR
            text = pytesseract.image_to_string(
                processed_image, 
                lang='chi_sim+eng',  # 支持中英文
                config='--psm 6'  # 假设是统一的文本块
            )
            
            if not text.strip():
                raise ConversionError("无法从图片中提取文本")
            
            # 清理和格式化文本
            markdown_content = self._clean_ocr_text(text)
            
            # 添加图片信息
            markdown_content = f"# 图片文档: {file_path.name}\n\n{markdown_content}"
            
            return markdown_content
            
        except Exception as e:
            logger.error(f"图片OCR转换失败: {e}")
            raise ConversionError(f"图片OCR转换失败: {e}")
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        预处理图片以提高OCR准确性
        
        Args:
            image: PIL图片对象
            
        Returns:
            处理后的图片
        """
        # 转换为灰度图
        if image.mode != 'L':
            image = image.convert('L')
        
        # 调整图片大小（如果太小的话）
        width, height = image.size
        if width < 1000 or height < 1000:
            # 放大图片
            scale_factor = max(1000 / width, 1000 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return image
    
    def _clean_ocr_text(self, text: str) -> str:
        """
        清理OCR识别的文本
        
        Args:
            text: OCR原始文本
            
        Returns:
            清理后的文本
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 处理换行
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                cleaned_lines.append(line)
        
        # 重新组织段落
        paragraphs = []
        current_paragraph = []
        
        for line in cleaned_lines:
            # 如果行很短或以句号结尾，可能是段落结束
            if len(line) < 20 or line.endswith(('。', '.', '！', '!', '？', '?')):
                current_paragraph.append(line)
                if current_paragraph:
                    paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
            else:
                current_paragraph.append(line)
        
        # 添加最后一个段落
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        return '\n\n'.join(paragraphs)
    
    def get_metadata(self, file_path: Path) -> Dict[str, Any]:
        """获取图片元数据"""
        metadata = super().get_metadata(file_path)
        
        try:
            with Image.open(file_path) as image:
                metadata.update({
                    "width": image.width,
                    "height": image.height,
                    "mode": image.mode,
                    "format": image.format,
                    "has_transparency": image.mode in ('RGBA', 'LA') or 'transparency' in image.info
                })
                
                # 获取EXIF信息（如果有）
                if hasattr(image, '_getexif') and image._getexif():
                    metadata["exif"] = dict(image._getexif())
                    
        except Exception as e:
            logger.warning(f"获取图片元数据失败: {e}")
        
        return metadata
    
    def is_tesseract_available(self) -> bool:
        """检查Tesseract是否可用"""
        try:
            pytesseract.get_tesseract_version()
            return True
        except Exception:
            return False
