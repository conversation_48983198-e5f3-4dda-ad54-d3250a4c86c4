"""
文本分割器
"""
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

from loguru import logger


@dataclass
class TextChunk:
    """文本块"""
    content: str
    start_index: int
    end_index: int
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseSplitter(ABC):
    """文本分割器基类"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    @abstractmethod
    def split_text(self, text: str) -> List[TextChunk]:
        """分割文本"""
        pass


class LengthBasedSplitter(BaseSplitter):
    """基于长度的文本分割器"""
    
    def split_text(self, text: str) -> List[TextChunk]:
        """
        基于长度分割文本
        
        Args:
            text: 输入文本
            
        Returns:
            文本块列表
        """
        chunks = []
        start = 0
        text_length = len(text)
        
        while start < text_length:
            end = min(start + self.chunk_size, text_length)
            
            # 尝试在句子边界分割
            if end < text_length:
                # 寻找最近的句子结束符
                sentence_end = self._find_sentence_boundary(text, start, end)
                if sentence_end > start:
                    end = sentence_end
            
            chunk_content = text[start:end].strip()
            if chunk_content:
                chunks.append(TextChunk(
                    content=chunk_content,
                    start_index=start,
                    end_index=end,
                    metadata={"chunk_type": "length_based"}
                ))
            
            # 计算下一个块的起始位置（考虑重叠）
            start = max(start + 1, end - self.chunk_overlap)
        
        return chunks
    
    def _find_sentence_boundary(self, text: str, start: int, end: int) -> int:
        """
        寻找句子边界
        
        Args:
            text: 文本
            start: 起始位置
            end: 结束位置
            
        Returns:
            句子边界位置
        """
        # 中文和英文句子结束符
        sentence_endings = ['。', '！', '？', '.', '!', '?', '\n\n']
        
        # 从end向start搜索句子结束符
        for i in range(end - 1, start, -1):
            if text[i] in sentence_endings:
                return i + 1
        
        return end


class ParagraphBasedSplitter(BaseSplitter):
    """基于段落的文本分割器"""
    
    def split_text(self, text: str) -> List[TextChunk]:
        """
        基于段落分割文本
        
        Args:
            text: 输入文本
            
        Returns:
            文本块列表
        """
        # 按段落分割
        paragraphs = re.split(r'\n\s*\n', text)
        chunks = []
        current_chunk = ""
        start_index = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前块加上新段落超过大小限制
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                # 保存当前块
                chunks.append(TextChunk(
                    content=current_chunk.strip(),
                    start_index=start_index,
                    end_index=start_index + len(current_chunk),
                    metadata={"chunk_type": "paragraph_based"}
                ))
                
                # 开始新块
                current_chunk = paragraph
                start_index = text.find(paragraph, start_index)
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                    start_index = text.find(paragraph, start_index)
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(TextChunk(
                content=current_chunk.strip(),
                start_index=start_index,
                end_index=start_index + len(current_chunk),
                metadata={"chunk_type": "paragraph_based"}
            ))
        
        return chunks


class SemanticSplitter(BaseSplitter):
    """基于语义的文本分割器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        super().__init__(chunk_size, chunk_overlap)
        self.section_patterns = [
            r'^#{1,6}\s+(.+)$',  # Markdown标题
            r'^第[一二三四五六七八九十\d]+[章节部分]\s*(.*)$',  # 中文章节
            r'^Chapter\s+\d+',  # 英文章节
            r'^\d+\.\s+(.+)$',  # 数字编号
            r'^[一二三四五六七八九十]+[、\.]\s*(.+)$',  # 中文编号
        ]
    
    def split_text(self, text: str) -> List[TextChunk]:
        """
        基于语义分割文本
        
        Args:
            text: 输入文本
            
        Returns:
            文本块列表
        """
        lines = text.split('\n')
        chunks = []
        current_chunk = ""
        current_section = ""
        start_index = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检查是否是新的章节
            section_title = self._extract_section_title(line)
            if section_title:
                # 保存当前块
                if current_chunk:
                    chunks.append(TextChunk(
                        content=current_chunk.strip(),
                        start_index=start_index,
                        end_index=start_index + len(current_chunk),
                        metadata={
                            "chunk_type": "semantic",
                            "section": current_section
                        }
                    ))
                
                # 开始新块
                current_chunk = line
                current_section = section_title
                start_index = text.find(line)
            else:
                # 添加到当前块
                if current_chunk:
                    current_chunk += "\n" + line
                else:
                    current_chunk = line
                    start_index = text.find(line)
                
                # 检查是否超过大小限制
                if len(current_chunk) > self.chunk_size:
                    # 尝试在合适的位置分割
                    split_point = self._find_split_point(current_chunk)
                    if split_point > 0:
                        # 分割块
                        first_part = current_chunk[:split_point]
                        second_part = current_chunk[split_point:]
                        
                        chunks.append(TextChunk(
                            content=first_part.strip(),
                            start_index=start_index,
                            end_index=start_index + len(first_part),
                            metadata={
                                "chunk_type": "semantic",
                                "section": current_section
                            }
                        ))
                        
                        current_chunk = second_part
                        start_index += len(first_part)
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(TextChunk(
                content=current_chunk.strip(),
                start_index=start_index,
                end_index=start_index + len(current_chunk),
                metadata={
                    "chunk_type": "semantic",
                    "section": current_section
                }
            ))
        
        return chunks
    
    def _extract_section_title(self, line: str) -> Optional[str]:
        """
        提取章节标题
        
        Args:
            line: 文本行
            
        Returns:
            章节标题，如果不是章节则返回None
        """
        for pattern in self.section_patterns:
            match = re.match(pattern, line)
            if match:
                return match.group(1) if match.groups() else line
        
        return None
    
    def _find_split_point(self, text: str) -> int:
        """
        寻找合适的分割点
        
        Args:
            text: 文本
            
        Returns:
            分割点位置
        """
        # 寻找段落边界
        paragraphs = text.split('\n\n')
        if len(paragraphs) > 1:
            # 找到中间位置的段落
            mid_point = len(paragraphs) // 2
            split_text = '\n\n'.join(paragraphs[:mid_point])
            return len(split_text)
        
        # 如果没有段落边界，寻找句子边界
        sentences = re.split(r'[。！？.!?]\s*', text)
        if len(sentences) > 1:
            mid_point = len(sentences) // 2
            split_text = '。'.join(sentences[:mid_point]) + '。'
            return len(split_text)
        
        # 最后选择中间位置
        return len(text) // 2


class TextSplitterFactory:
    """文本分割器工厂"""
    
    @staticmethod
    def create_splitter(
        splitter_type: str = "length",
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ) -> BaseSplitter:
        """
        创建文本分割器
        
        Args:
            splitter_type: 分割器类型
            chunk_size: 块大小
            chunk_overlap: 重叠大小
            
        Returns:
            文本分割器实例
        """
        if splitter_type == "length":
            return LengthBasedSplitter(chunk_size, chunk_overlap)
        elif splitter_type == "paragraph":
            return ParagraphBasedSplitter(chunk_size, chunk_overlap)
        elif splitter_type == "semantic":
            return SemanticSplitter(chunk_size, chunk_overlap)
        else:
            raise ValueError(f"不支持的分割器类型: {splitter_type}")
