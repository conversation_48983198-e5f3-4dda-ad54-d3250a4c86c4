"""
大文件处理器
"""
import asyncio
import json
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional, Iterator
from dataclasses import dataclass

from loguru import logger

from config import settings
from database.models import SessionLocal
from database.operations import KnowledgeBaseOperations, KnowledgePointOperations
from converters.converter_factory import converter_factory
from extractors.knowledge_extractor import KnowledgeExtractor
from extractors.text_splitter import TextSplitterFactory
from core.prompt_generator import ExpertInput
from .processor import ProcessingResult


@dataclass
class FileSegment:
    """文件段"""
    segment_id: int
    start_byte: int
    end_byte: int
    content: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LargeFileHandler:
    """大文件处理器"""
    
    def __init__(self):
        self.extractor = KnowledgeExtractor()
        self.segment_size = 10 * 1024 * 1024  # 10MB per segment
        self.max_concurrent_segments = 5
    
    async def process_large_file(
        self,
        file_path: Path,
        industry: str,
        expert_input: ExpertInput
    ) -> ProcessingResult:
        """
        处理大文件
        
        Args:
            file_path: 文件路径
            industry: 行业
            expert_input: 专家输入
            
        Returns:
            处理结果
        """
        import time
        start_time = time.time()
        
        try:
            logger.info(f"开始处理大文件: {file_path}")
            
            # 检查文件类型
            if not converter_factory.is_supported(file_path):
                return ProcessingResult(
                    success=False,
                    error_message=f"不支持的文件类型: {file_path.suffix}"
                )
            
            # 分段处理策略
            if file_path.suffix.lower() == '.pdf':
                return await self._process_large_pdf(file_path, industry, expert_input)
            elif file_path.suffix.lower() in ['.docx', '.doc']:
                return await self._process_large_word(file_path, industry, expert_input)
            elif file_path.suffix.lower() in ['.txt', '.md']:
                return await self._process_large_text(file_path, industry, expert_input)
            else:
                # 默认处理方式：先转换再分段
                return await self._process_large_generic(file_path, industry, expert_input)
                
        except Exception as e:
            logger.error(f"大文件处理失败: {e}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    async def _process_large_pdf(
        self,
        file_path: Path,
        industry: str,
        expert_input: ExpertInput
    ) -> ProcessingResult:
        """处理大PDF文件"""
        import PyPDF2
        
        try:
            # 按页分段处理PDF
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                logger.info(f"PDF总页数: {total_pages}")
                
                # 创建知识库记录
                db = SessionLocal()
                try:
                    kb_ops = KnowledgeBaseOperations(db)
                    knowledge_base = kb_ops.create_knowledge_base(
                        industry=industry,
                        title=f"{file_path.stem} (大文件)",
                        keywords=[],
                        description=f"大PDF文件 {file_path.name} 的分段处理结果",
                        knowledge_source=str(file_path)
                    )
                    
                    # 分批处理页面
                    pages_per_batch = 10
                    total_knowledge_points = 0
                    
                    for start_page in range(0, total_pages, pages_per_batch):
                        end_page = min(start_page + pages_per_batch, total_pages)
                        
                        # 提取这批页面的文本
                        batch_text = ""
                        for page_num in range(start_page, end_page):
                            try:
                                page_text = pdf_reader.pages[page_num].extract_text()
                                batch_text += f"\n\n## 第 {page_num + 1} 页\n\n{page_text}"
                            except Exception as e:
                                logger.warning(f"提取第 {page_num + 1} 页失败: {e}")
                        
                        if batch_text.strip():
                            # 处理这批文本
                            batch_result = await self._process_text_segment(
                                batch_text, expert_input, knowledge_base.id, industry
                            )
                            total_knowledge_points += batch_result
                            
                            logger.info(f"处理页面 {start_page + 1}-{end_page}，提取 {batch_result} 个知识点")
                    
                    return ProcessingResult(
                        success=True,
                        knowledge_base_id=knowledge_base.id,
                        knowledge_points_count=total_knowledge_points,
                        metadata={"total_pages": total_pages, "processing_method": "page_batch"}
                    )
                    
                finally:
                    db.close()
                    
        except Exception as e:
            logger.error(f"大PDF处理失败: {e}")
            raise
    
    async def _process_large_text(
        self,
        file_path: Path,
        industry: str,
        expert_input: ExpertInput
    ) -> ProcessingResult:
        """处理大文本文件"""
        try:
            # 创建知识库记录
            db = SessionLocal()
            try:
                kb_ops = KnowledgeBaseOperations(db)
                knowledge_base = kb_ops.create_knowledge_base(
                    industry=industry,
                    title=f"{file_path.stem} (大文件)",
                    keywords=[],
                    description=f"大文本文件 {file_path.name} 的分段处理结果",
                    knowledge_source=str(file_path)
                )
                
                total_knowledge_points = 0
                
                # 分段读取文件
                async for segment in self._read_file_segments(file_path):
                    if segment.content.strip():
                        batch_result = await self._process_text_segment(
                            segment.content, expert_input, knowledge_base.id, industry
                        )
                        total_knowledge_points += batch_result
                        
                        logger.info(f"处理段 {segment.segment_id}，提取 {batch_result} 个知识点")
                
                return ProcessingResult(
                    success=True,
                    knowledge_base_id=knowledge_base.id,
                    knowledge_points_count=total_knowledge_points,
                    metadata={"processing_method": "text_segments"}
                )
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"大文本文件处理失败: {e}")
            raise
    
    async def _process_large_generic(
        self,
        file_path: Path,
        industry: str,
        expert_input: ExpertInput
    ) -> ProcessingResult:
        """处理其他类型的大文件"""
        try:
            # 先转换整个文件
            logger.info("转换大文件为Markdown...")
            conversion_result = converter_factory.convert_file(file_path)
            
            if not conversion_result["success"]:
                return ProcessingResult(
                    success=False,
                    error_message=f"文档转换失败: {conversion_result['error']}"
                )
            
            markdown_content = conversion_result["markdown_content"]
            
            # 将转换后的内容保存到临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
                temp_file.write(markdown_content)
                temp_path = Path(temp_file.name)
            
            try:
                # 按文本方式处理临时文件
                result = await self._process_large_text(temp_path, industry, expert_input)
                
                # 更新元数据
                if result.metadata:
                    result.metadata["original_file"] = str(file_path)
                    result.metadata["conversion_metadata"] = conversion_result["metadata"]
                
                return result
                
            finally:
                # 清理临时文件
                temp_path.unlink()
                
        except Exception as e:
            logger.error(f"通用大文件处理失败: {e}")
            raise
    
    async def _read_file_segments(self, file_path: Path) -> Iterator[FileSegment]:
        """分段读取文件"""
        segment_id = 0
        
        with open(file_path, 'r', encoding='utf-8') as file:
            while True:
                # 读取一个段的内容
                content = file.read(self.segment_size)
                if not content:
                    break
                
                # 尝试在句子边界结束
                if len(content) == self.segment_size:
                    # 寻找最后一个句子结束符
                    last_sentence_end = max(
                        content.rfind('。'),
                        content.rfind('！'),
                        content.rfind('？'),
                        content.rfind('.'),
                        content.rfind('!'),
                        content.rfind('?'),
                        content.rfind('\n\n')
                    )
                    
                    if last_sentence_end > len(content) * 0.8:  # 如果句子结束符在后80%位置
                        # 调整文件指针
                        file.seek(file.tell() - (len(content) - last_sentence_end - 1))
                        content = content[:last_sentence_end + 1]
                
                yield FileSegment(
                    segment_id=segment_id,
                    start_byte=file.tell() - len(content),
                    end_byte=file.tell(),
                    content=content
                )
                
                segment_id += 1
    
    async def _process_text_segment(
        self,
        text: str,
        expert_input: ExpertInput,
        knowledge_base_id: int,
        industry: str
    ) -> int:
        """
        处理文本段
        
        Args:
            text: 文本内容
            expert_input: 专家输入
            knowledge_base_id: 知识库ID
            industry: 行业
            
        Returns:
            提取的知识点数量
        """
        try:
            # 提取知识点
            extraction_result = await self.extractor.extract_knowledge_points(
                text, expert_input, max_concurrent=2  # 降低并发数以节省资源
            )
            
            # 保存知识点
            db = SessionLocal()
            try:
                kp_ops = KnowledgePointOperations(db)
                saved_count = 0
                
                for point in extraction_result.knowledge_points:
                    try:
                        kp_ops.create_knowledge_point(
                            knowledge_base_id=knowledge_base_id,
                            industry=industry,
                            material_type=expert_input.material_type,
                            knowledge_point=point.content,
                            prompt=json.dumps({
                                "title": point.title,
                                "keywords": point.keywords,
                                "summary": point.summary,
                                "industry_info": point.industry_info,
                                "confidence": point.confidence
                            }, ensure_ascii=False)
                        )
                        saved_count += 1
                    except Exception as e:
                        logger.warning(f"保存知识点失败: {e}")
                
                return saved_count
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"处理文本段失败: {e}")
            return 0
